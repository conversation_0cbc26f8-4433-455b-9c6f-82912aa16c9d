<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management</title>

    <style>
        .index-success {
            width: 10px;
            height: 10px;
            background-color: green;
            border-radius: 50%;
            display: inline-block;
            margin-right: 20px;
            vertical-align: middle;
        }
        .index-fail {
            width: 10px;
            height: 10px;
            background-color: red;
            border-radius: 50%;
            display: inline-block;
            margin-right: 20px;
            vertical-align: middle;
        }
        .index-label {
            margin-right: 10px;
            font-size: 0.9rem;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2 class="mb-4">文档管理</h2>



        <form id="uploadFile" style="">
            选择文件：
            <input type="file" name="filesToUpload" accept=".pdf, .txt, .doc" id="filesToUpload" multiple>
        </form>
        <button class="btn btn-primary" style="margin-top: 15px" onclick="uploadDocument()">上传</button>
        <hr>
        <h4 class="mb-3">文档列表</h4>
        <div class="list-group">
            {% for document in documents %}
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div style="max-width: 400px">
                        <h5 class="mb-1">{{ document.name }}</h5>
                        <p class="mb-1">大小: {{ document.size }}</p>
                        <p>上传时间: {{ document.upload_time }}</p>
                    </div>
                    <span>
                        <span class="index-label">索引状态:</span>
                        <span class="{% if document.index_status %}index-success{% else %}index-fail{% endif %}"></span>
                        <button class="btn btn-success btn-sm" onclick="downloadDocument()">下载</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteDocument()">删除</button>
                    </span>
                </div>
            {% endfor %}
            <!-- Repeat for each document -->
        </div>
    </div>

    <script>
        function uploadDocument() {
            var formData = new FormData();
            var files = $('#filesToUpload')[0].files;
            for (var i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }
            $.ajax({
                url: 'upload/',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                headers: {
                    'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(data) {
                    if(data.status === 1){
                        alert("上传成功！")
                        filecontrol();
                    }else if(data.status ===0){
                        $('#loginModal').modal('show');
                    }else if(data.status ===-2){
                        alert("上传文件不能为空！")
                    }
                },
                error: function() {

                }
            });
        }


        function downloadDocument() {
            // Implement download functionality
            alert('Download functionality not implemented.');
        }

        function deleteDocument() {
            // Implement delete functionality
            alert('Delete functionality not implemented.');
        }
    </script>

</body>
</html>
