{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="csrf-token" content="{{ csrf_token }}">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>用户用量情况查询</title>
<script src="{% static 'js/chart.js' %}"></script>
<script src="{% static 'js/jquery.js' %}"></script>
</head>
<body>
<div style="max-width: 800px; margin: 2em auto;">
    <h2>月度用量</h2>
    <div style="margin-bottom: 20px">
        <label for="yearSelect">年份:</label>
        <select id="yearSelect">
            <!-- Options will be populated dynamically -->
        </select>

        <label for="monthSelect">月份:</label>
        <select id="monthSelect">
            <option value="1">一月</option>
            <option value="2">二月</option>
            <option value="3">三月</option>
            <option value="4">四月</option>
            <option value="5">五月</option>
            <option value="6">六月</option>
            <option value="7">七月</option>
            <option value="8">八月</option>
            <option value="9">九月</option>
            <option value="10">十月</option>
            <option value="11">十一月</option>
            <option value="12">十二月</option>
        </select>
        <button onclick="getusagedata()">查询</button>
    </div>
    <div>
        <button id="switchbutton" onclick="switch2price()">以模型价格显示</button>
    </div>
    <canvas id="myStackedBarChart"></canvas>
</div>

<script>
// Helper function to generate all days in a month
function getDaysInMonth(year, month) {
    var date = new Date(year, month, 1);
    var days = [];
    while (date.getMonth() === month) {
        days.push(new Date(date).getDate());
        date.setDate(date.getDate() + 1);
    }
    return days;
}

// Assuming you want to show the current month
const currentDate = new Date();
const currentYear = currentDate.getFullYear();
const currentMonth = currentDate.getMonth();

// 添加最近三年的选项
const yearSelect = document.getElementById('yearSelect');
for (let i = 0; i < 3; i++) {
    const yearOption = document.createElement('option');
    yearOption.value = currentYear - i;
    yearOption.textContent = currentYear - i;
    yearSelect.appendChild(yearOption);
}

// 保存 Chart 实例的变量
let myChart = null;
let model_price = {{ price|safe }};
let token_data = {{data|safe}}

function update_usage(usage_data,Month,Year,switch2price=false) {
    // 先销毁已有图表实例（如果存在的话）
    if (myChart) {
        myChart.destroy();
    }
    if (typeof Month === "string"){
        Month = parseInt(Month,10) - 1
    }
    // Get all days in the month
    const daysInMonth = getDaysInMonth(Year, Month);
    const ctx = document.getElementById('myStackedBarChart').getContext('2d');

    // 构建 datasets
    let labels = [];
    const tokenDataByModel = {};
    let ytext = null;
    usage_data.forEach(item => {
        const model = item.use_model;
        if (!labels.includes(model)) {
            labels.push(model);
        }
        const date = item.use_time;
        const token1 = item.prompt_token;
        const token2 = item.output_token;
        if (!tokenDataByModel[model]) {
            tokenDataByModel[model] = {};
        }
        console.log(model_price)
        if(switch2price){
            ytext = "价格（￥）"
            if(model_price[model]){
                tokenDataByModel[model][date] = token1/1000000*model_price[model][0] + token2/1000000*model_price[model][0];
            }
        }else{
            ytext = "Tokens用量"
            tokenDataByModel[model][date] = token1 + 2 * token2;
        }



    });

    let backgroundColor = ['rgba(54, 162, 235, 0.8)', 'rgba(255, 99, 132, 0.8)', 'rgba(144, 238, 144, 0.8)', 'rgba(0, 100, 0, 0.8)', 'rgba(216, 191, 216, 0.8)', 'rgba(148, 0, 211, 0.8)'];
    let borderColor = ['rgba(54, 162, 235, 1)', 'rgba(255, 99, 132, 1)', 'rgba(144, 238, 144, 1)', 'rgba(0, 100, 0, 1)', 'rgba(216, 191, 216, 1)', 'rgba(148, 0, 211, 1)'];
    let datasets = [];
    let i = 0;
    labels.forEach(item => {
        var data = {
            label: item,
            data: daysInMonth.map(date => tokenDataByModel[item] && tokenDataByModel[item][date] ? tokenDataByModel[item][date] : 0),
            backgroundColor: backgroundColor[i],
            borderColor: borderColor[i],
            borderWidth: 1,
        };
        datasets.push(data);
        i += 1;
    });

    // 创建新的图表实例
    myChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: daysInMonth.map(day => `${(Month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`), // X-axis labels - all days in the month
            datasets: datasets
        },
        options: {
            scales: {
                y: {
                    stacked: true,
                    beginAtZero: true,
                title: {
                display: true,
                text: ytext // 添加纵轴标题
            }

                },
                x: {
                    stacked: true,
                    title: {
                    display: true,
                    text: '日期' // 添加横轴标题
                },
                    ticks: {
                        callback: function (val, index) {
                            // Change this value to display more or less labels
                            return index % 5 === 0 ? this.getLabelForValue(val) : '';
                        },
                        autoSkip: false
                    }

                }
            },
            plugins: {
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            }
        }
    });
}

function getusagedata() {
    var month = document.getElementById("monthSelect").value;
    var year = document.getElementById("yearSelect").value;
    var time = { "month": month, "year": year };
    $.ajax({
        url: '/find_usage',
        type: 'GET',
        dataType: 'json',
        data: time,
        headers: {
            'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
        },
        success: function (data) {
            token_data = data.data;
            update_usage(data.data,month,year);
        },
        error: function (error) {
            console.error('usage fail:', error);
        }
    });
}
function switch2price(){
   var month = document.getElementById("monthSelect").value;
   var year = document.getElementById("yearSelect").value;
   var button = document.getElementById("switchbutton")
   if (button.innerText === "以模型价格显示"){
       update_usage(token_data,month,year,true);
      button.innerText="以模型用量显示"
   }else{
       update_usage(token_data,month,year,false);
       button.innerText="以模型价格显示"
   }


}
update_usage(token_data,currentMonth,currentYear);

</script>
</body>
</html>
