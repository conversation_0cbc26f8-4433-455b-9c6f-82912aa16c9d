{% load static %}
<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文案平台转换</title>
    <style>
        .container {
            max-width: 90%;
            margin-top: 15px;
            background: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            margin-right: auto;
            margin-left: auto;
            position: relative;
        }

        .page-title {
            text-align: center;
            color: #333;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .section-label {
            font-size: 18px;
            font-weight: 600;
            color: #444;
            margin-bottom: 12px;
            display: block;
        }

        #original-content {
            width: 100%;
            height: 180px;
            min-height: 120px;
            max-height: 400px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            box-sizing: border-box;
        }

        #original-content:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        #original-content::placeholder {
            color: #999;
            font-style: italic;
        }

        .buttons-section {
            margin-bottom: 30px;
        }

        .platform-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .platform-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .platform-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .platform-btn:hover::before {
            left: 100%;
        }

        .weibo-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .weibo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .xiaohongshu-btn {
            background: linear-gradient(135deg, #ff4757, #ff3742);
            box-shadow: 0 4px 15px rgba(255, 71, 87, 0.3);
        }

        .xiaohongshu-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 71, 87, 0.4);
        }

        .wechat-btn {
            background: linear-gradient(135deg, #00d2ff, #3a7bd5);
            box-shadow: 0 4px 15px rgba(0, 210, 255, 0.3);
        }

        .wechat-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 210, 255, 0.4);
        }

        .douyin-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .douyin-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .platform-btn:active {
            transform: translateY(-1px);
        }

        .platform-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .result-section {
            margin-top: 30px;
        }

        #conversion-result {
            width: 100%;
            min-height: 150px;
            max-height: 500px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            background-color: #f8f9fa;
            font-size: 16px;
            line-height: 1.6;
            overflow-y: auto;
            resize: vertical;
            box-sizing: border-box;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .copy-btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .status-message {
            padding: 12px;
            border-radius: 6px;
            margin-top: 15px;
            text-align: center;
            font-weight: 500;
            display: none;
        }

        .success-message {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .platform-icon {
            margin-right: 8px;
            font-size: 18px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin-top: 10px;
            }
            
            .platform-buttons {
                grid-template-columns: 1fr;
            }
            
            .page-title {
                font-size: 24px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="page-title">✨ 文案平台转换</h1>
        
        <div class="input-section">
            <label for="original-content" class="section-label">📝 原始文案内容</label>
            <textarea 
                id="original-content" 
                placeholder="请输入您要转换的文案内容...&#10;&#10;支持各种类型的文案：&#10;• 产品介绍&#10;• 营销文案&#10;• 活动宣传&#10;• 品牌故事"
            ></textarea>
        </div>

        <div class="buttons-section">
            <label class="section-label">🚀 选择目标平台</label>
            <div class="platform-buttons">
                <button class="platform-btn weibo-btn" onclick="convertToPlatform('weibo')">
                    <span class="platform-icon">📱</span>转换成微博文案
                </button>
                <button class="platform-btn xiaohongshu-btn" onclick="convertToPlatform('xiaohongshu')">
                    <span class="platform-icon">📔</span>转换成小红书文案
                </button>
                <button class="platform-btn wechat-btn" onclick="convertToPlatform('wechat')">
                    <span class="platform-icon">💬</span>转换成微信公众号文案
                </button>
                <button class="platform-btn douyin-btn" onclick="convertToPlatform('douyin')">
                    <span class="platform-icon">🎵</span>转换成抖音文案
                </button>
            </div>
        </div>

        <div class="loading-spinner" id="loading-spinner">
            <div class="spinner"></div>
            <p>正在转换中，请稍候...</p>
        </div>

        <div class="result-section">
            <label for="conversion-result" class="section-label">✅ 转换结果</label>
            <textarea 
                id="conversion-result" 
                placeholder="转换结果将在这里显示..."
                readonly
            ></textarea>
            <button class="copy-btn" id="copy-btn" onclick="copyResult()" style="display: none;">
                📋 复制结果
            </button>
        </div>

        <div class="status-message" id="status-message"></div>
    </div>

    <script>
        function handleWebSocketMessage(data) {
            if (data.target === 6) { // 文案平台转换的target
                if (data.status === 1) {
                    // 转换完成
                    showLoading(false);
                    disableButtons(false);
                    showMessage('✅ 转换完成！', 'success');
                } else if (data.status === 0) {
                    // 转换失败
                    showLoading(false);
                    disableButtons(false);
                    showMessage('❌ 转换失败，请稍后重试！', 'error');
                } else if (data.message) {
                    // 接收转换结果
                    const resultTextarea = document.getElementById('conversion-result');
                    resultTextarea.value = data.message.replace(/<br>/g, '\n');
                    document.getElementById('copy-btn').style.display = 'inline-block';
                }
            }
        }

        function convertToPlatform(platform) {
            const originalContent = document.getElementById('original-content').value.trim();
            
            if (!originalContent) {
                showMessage('请先输入要转换的文案内容！', 'error');
                return;
            }

            // 检查全局socket是否可用（来自main.html）
            if (typeof socket === 'undefined' || !socket || socket.readyState !== WebSocket.OPEN) {
                showMessage('连接未建立，请刷新页面重试', 'error');
                return;
            }

            // 显示加载状态
            showLoading(true);
            disableButtons(true);
            
            // 构建发送数据
            const platformNames = {
                'weibo': '微博',
                'xiaohongshu': '小红书', 
                'wechat': '微信公众号',
                'douyin': '抖音'
            };

            const requestData = {
                source: 6,  // 对应consumers.py中的文案平台转换
                model: 'gpt-4o',  // 可以根据需要修改模型
                originalContent: originalContent,
                targetPlatform: platformNames[platform],
                platformKey: platform
            };

            // 发送WebSocket消息到全局socket
            socket.send(JSON.stringify(requestData));
            
            showMessage(`🚀 正在转换为${platformNames[platform]}文案...`, 'info');
        }

        function copyResult() {
            const resultText = document.getElementById('conversion-result').value;
            if (!resultText) {
                showMessage('没有可复制的内容！', 'error');
                return;
            }

            navigator.clipboard.writeText(resultText).then(() => {
                showMessage('📋 内容已复制到剪贴板！', 'success');
            }).catch(err => {
                // 兼容性处理
                const textArea = document.createElement('textarea');
                textArea.value = resultText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showMessage('📋 内容已复制到剪贴板！', 'success');
            });
        }

        function showLoading(show) {
            document.getElementById('loading-spinner').style.display = show ? 'block' : 'none';
        }

        function disableButtons(disable) {
            const buttons = document.querySelectorAll('.platform-btn');
            buttons.forEach(button => {
                button.disabled = disable;
            });
        }

        function showMessage(message, type) {
            const messageElement = document.getElementById('status-message');
            messageElement.textContent = message;
            messageElement.className = `status-message ${type}-message`;
            messageElement.style.display = 'block';
            
            // 3秒后自动隐藏消息
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 3000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('文案平台转换器已加载完成');
        });
    </script>
</body>

</html> 