import os
import string
import re


import openai
import urllib.request, json

from langchain_openai import ChatOpenAI
from serpapi import GoogleSearch
from dotenv import load_dotenv
from IPython.utils import io



load_dotenv(r'D:\硕士\aiagent\gpt_CWSC\.env')
openai.api_key = os.getenv("OPENAI_API_KEY")
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY_2")
print(deepseek_api_key)
print(openai.api_key)
serpapi_key = '61da5eaf67a09ac0ed1397dee8debd0f7749e6551c545be8bcedc572d6c3f35b'

#1.prompt
#例如“问题1：谁活得更久，穆罕默德·阿里还是艾伦·图灵？”。
#问题2：询问 Craigslist 创始人的出生日期。 为了回答这个问题，首先需要知道谁是创始人，然后才能找到他的出生日期。
"""第三个例子： 询问乔治·华盛顿的外祖父是谁。 为了回答这个问题，需要知道乔治·华盛顿的母亲是谁，然后才能找到她父亲的名字。

第四个例子： 询问《大白鲨》和《007：皇家赌场》的导演是否来自同一个国家。 为了回答这个问题，需要知道这两部电影的导演是谁，以及他们各自的国籍。"""

prompt = ['''Question: Who lived longer, Muhammad Ali or Alan Turing?
Are follow up questions needed here: Yes.
Follow up: How old was Muhammad Ali when he died?
Intermediate answer: Muhammad Ali was 74 years old when he died.
Follow up: How old was Alan Turing when he died?
Intermediate answer: Alan Turing was 41 years old when he died.
So the final answer is: Muhammad Ali 

Question: When was the founder of craigslist born?
Are follow up questions needed here: Yes.
Follow up: Who was the founder of craigslist?
Intermediate answer: Craigslist was founded by Craig Newmark.
Follow up: When was Craig Newmark born?
Intermediate answer: Craig Newmark was born on December 6, 1952.
So the final answer is: December 6, 1952

Question: Who was the maternal grandfather of George Washington?
Are follow up questions needed here: Yes.
Follow up: Who was the mother of George Washington?
Intermediate answer: The mother of George Washington was Mary Ball Washington.
Follow up: Who was the father of Mary Ball Washington?
Intermediate answer: The father of Mary Ball Washington was Joseph Ball.
So the final answer is: Joseph Ball 

Question: Are both the directors of Jaws and Casino Royale from the same country? 
Are follow up questions needed here: Yes. 
Follow up: Who is the director of Jaws? 
Intermediate Answer: The director of Jaws is Steven Spielberg. 
Follow up: Where is Steven Spielberg from? 
Intermediate Answer: The United States. 
Follow up: Who is the director of Casino Royale? 
Intermediate Answer: The director of Casino Royale is Martin Campbell. 
Follow up: Where is Martin Campbell from? 
Intermediate Answer: New Zealand. 
So the final answer is: No

Question: ''',
'''
Are follow up questions needed here:''', ]
def extract_follow_ups_and_intermediate_answers(text):
    # 提取子问题和答案到两个列表中
    lines = text.strip().split('\n')

    # 存储提取的 "Follow up:" 内容
    follow_ups = []
    Intermediate_answer = []
    final_answer = ""

    # 遍历每一行
    for line in lines:
        # 检查当前行是否以 "Follow up:" 开头
        if line.startswith("Follow up:"):
            # 提取 "Follow up:" 后的内容，并去掉前后的空格
            follow_up_content = line[len("Follow up:"):].strip()
            follow_ups.append(follow_up_content)  # 添加到列表中
        elif line.startswith("Intermediate answer:"):
            Intermediate_answer.append(line[len("Intermediate answer:"):].strip())
        elif line.startswith("So the final answer is:"):
            final_answer = line[len("So the final answer is:"):].strip()

    # 返回提取的 "Follow up:" 内容

    return follow_ups, Intermediate_answer, final_answer


def promptf(question, prompt, intermediate="\nIntermediate answer:", followup="Follow up:",
            finalans='\nSo the final answer is:'):
    """
    根据给定的问题和prompt模板，通过逐步提问和回答生成最终答案。

    参数:
    - question: 需要回答的问题字符串
    - prompt: 包含初始prompt结构的列表，格式为 [开头部分, 后续问题提示]
    - intermediate: 中间答案的前缀（默认为换行+Intermediate answer:）
    - followup: 后续问题的前缀（默认为Follow up:）
    - finalans: 最终答案的前缀（默认为换行+So the final answer is:）
    返回:
    - 完整的prompt和生成的回复文本（包含所有步骤和最终答案）
    """

    # 初始化当前prompt：将问题插入到prompt模板的开头和结尾之间prompt[1]是Are follow up questions needed here:'
    cur_prompt = prompt[0] + question + prompt[1]

    # 打印初始prompt（用于调试或显示）
    print(cur_prompt, end='')

    # 调用GPT模型生成初始回复（包含第一个后续问题或中间答案）
    ret_text = call_gpt(cur_prompt, intermediate)
    follow_up_questions,Intermediate_answers,finalans = extract_follow_ups_and_intermediate_answers(ret_text)
    for i in range(len(follow_up_questions)):
        # 通过外部API（如搜索引擎）获取问题的答案
        external_answer = get_answer(follow_up_questions[i])
        if external_answer is not None:
            # 将外部答案作为中间答案追加到当前prompt中
            cur_prompt += '\n' + followup + ' ' + follow_up_questions[i] + intermediate + ' ' + external_answer + '.'
        else:
            # 极少数情况下，外部API无答案，直接让GPT生成后续内容
            cur_prompt += '\n' + followup + ' ' + follow_up_questions[i] + intermediate + ' ' + Intermediate_answers[i] + '.'  # 追加中间答案的前缀



    # 循环处理所有后续问题，直到没有更多Follow up为止


    # 检查最终答案是否已生成，若未生成则手动添加前缀并调用GPT
    cur_prompt+='\n'+"So the final answer is:"
    ret_text = call_gpt(cur_prompt, '\n')

    # 返回完整的prompt和最终回复（包含所有步骤和答案）
    return cur_prompt + ret_text


def google(question):
    """
    使用SerpApi搜索问题并提取答案。

    参数:
    question (str): 需要查询的问题字符串，例如："What is the boiling point of water?"。

    返回:
    str or None: 如果找到答案则返回字符串（如"100°C"），否则返回None。

    注意事项:
    - 需提前安装SerpApi库：`pip install google-search-results`
    - 需配置有效的SerpApi API密钥（serpapi_key）
    - 依赖SerpApi的响应结构，若API返回格式变更可能需要调整代码
    - 优先提取答案框内容，其次为高亮片段或首个自然搜索结果摘要
    """

    # 配置SerpApi请求参数
    params = {
        "api_key": serpapi_key,  # 替换为你的SerpApi API密钥
        "engine": "google",  # 使用Google搜索引擎
        "q": question,  # 查询的问题
        "google_domain": "google.com",
        "gl": "us",  # 国家代码（美国）
        "hl": "en"  # 语言设置（英语）
    }

    # 抑制GoogleSearch的输出（如进度条等）
    with io.capture_output() as captured:
        search = GoogleSearch(params)  # 初始化搜索对象
        res = search.get_dict()  # 获取搜索结果的字典格式响应

    # 从响应中提取答案的优先级逻辑：
    # 1. 优先检查答案框中的直接答案（如计算、定义等）
    if 'answer_box' in res.keys() and 'answer' in res['answer_box'].keys():
        toret = res['answer_box']['answer']
    # 2. 次选答案框中的摘要片段（可能包含关键信息）
    elif 'answer_box' in res.keys() and 'snippet' in res['answer_box'].keys():
        toret = res['answer_box']['snippet']
    # 3. 若存在高亮关键词片段，提取第一个
    elif 'answer_box' in res.keys() and 'snippet_highlighted_words' in res['answer_box'].keys():
        toret = res['answer_box']["snippet_highlighted_words"][0]
    # 4. 若以上均无，则取首个自然搜索结果的摘要
    elif 'organic_results' in res and len(res['organic_results']) > 0 and 'snippet' in res["organic_results"][0].keys():
        toret = res["organic_results"][0]['snippet']
    else:
        toret = None  # 未找到答案时返回None

    return toret


def get_answer(question):
    """
    使用外部API（如搜索引擎）获取问题的答案。

    参数:
    question (str): 需要查询的问题字符串，例如："What is the boiling point of water?"。

    返回:
    str or None: 如果找到答案则返回字符串（如"100°C"），否则返回None。
    """


    return google(question)


def call_gpt(cur_prompt, stop):
    """
    调用 OpenAI 的 GPT 模型生成文本。

    参数:
        cur_prompt (str): 当前的提示文本，作为 GPT 模型的输入。
        stop (str 或 list): 停止生成的标识符，可以是字符串或列表。

    返回:
        str: GPT 模型生成的文本。
    """
    # 调用 OpenAI 的 API 生成文本


    # llm = ChatDeepSeek(
    #     model="deepseek-ai/DeepSeek-V3",
    #     temperature=0,
    #     max_tokens=None,
    #     timeout=40,
    #     max_retries=2,
    #     api_base="https://api.siliconflow.cn",
    #     api_key=deepseek_api_key,
    # )

    ##第二种deepseek的调用方式
    llm = ChatOpenAI(
        model='deepseek-chat',
        openai_api_key=deepseek_api_key,
        openai_api_base='https://api.deepseek.com/v1',
        max_tokens=4096,
        temperature=1.5
    )
    res = llm.invoke(cur_prompt).content
    print(res)

    # 从 API 返回的结果中提取生成的文本
    returned = res

    # 将生成的文本格式化为绿色并打印（不换行）
    print(greenify(returned), end='')

    # 返回生成的子问题以及结果，这个结果是没有调用外部搜索API的
    return returned


def extract_answer(generated):
    # 检查生成的文本中是否包含换行符
    if '\n' not in generated:
        last_line = generated  # 如果没有换行符，直接将生成的文本赋值给 last_line
    else:
        last_line = generated.split('\n')[-1]  # 否则，获取最后一行

    # 检查最后一行中是否包含冒号
    if ':' not in last_line:
        after_colon = last_line  # 如果没有冒号，直接将最后一行赋值给 after_colon
    else:
        after_colon = generated.split(':')[-1]  # 否则，获取冒号后面的内容

    # 去掉 after_colon 开头的空格
    if ' ' == after_colon[0]:
        after_colon = after_colon[1:]
    # 去掉 after_colon 末尾的句号
    if '.' == after_colon[-1]:
        after_colon = after_colon[:-1]

    return after_colon  # 返回提取的答案


def extract_question(generated):
    # 检查生成的文本中是否包含换行符
    if '\n' not in generated:
        last_line = generated  # 如果没有换行符，直接将生成的文本赋值给 last_line
    else:
        last_line = generated.split('\n')[-1]  # 否则，获取最后一行

    # 检查最后一行中是否包含 "Follow up:"
    if 'Follow up:' not in last_line:
        print('we probably should never get here...' + generated)  # 如果没有，打印警告信息

    # 检查最后一行中是否包含冒号
    if ':' not in last_line:
        after_colon = last_line  # 如果没有冒号，直接将最后一行赋值给 after_colon
    else:
        after_colon = generated.split(':')[-1]  # 否则，获取冒号后面的内容

    # 去掉 after_colon 开头的空格
    if ' ' == after_colon[0]:
        after_colon = after_colon[1:]
    # 检查 after_colon 末尾是否是问号
    if '?' != after_colon[-1]:
        print('we probably should never get here...' + generated)  # 如果不是，打印警告信息

    return after_colon  # 返回提取的问题


def get_last_line(generated):
    # 检查生成的文本中是否包含换行符
    if '\n' not in generated:
        last_line = generated  # 如果没有换行符，直接将生成的文本赋值给 last_line
    else:
        last_line = generated.split('\n')[-1]  # 否则，获取最后一行

    return last_line  # 返回最后一行

def greenify(input):
    # 使用 ANSI 转义序列将输入文本的背景颜色设置为绿色
    # "\x1b[102m" 表示设置背景颜色为绿色
    # "\x1b[0m" 表示重置所有样式（恢复默认颜色）
    return "\x1b[102m" + input + "\x1b[0m"

def yellowfy(input):
    # 使用 ANSI 转义序列将输入文本的背景颜色设置为黄色
    # "\x1b[106m" 表示设置背景颜色为黄色
    # "\x1b[0m" 表示重置所有样式（恢复默认颜色）
    return "\x1b[106m" + input + "\x1b[0m"


question = "What is the hometown of the reigning men's U.S. Open champion?"
ret = promptf(question, prompt)
print(ret)
clean_ans = extract_answer(ret)
#green background means it was generated by GPT-3
#cyan background means it was returned by Google Search






