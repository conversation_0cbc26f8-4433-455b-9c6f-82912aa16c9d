from itertools import combinations
from math import factorial

def calculate_shapley_values(n, coalition_values):
    """
    计算n人团队的Shapley值奖励分配

    Args:
        n: 团队成员数量
        coalition_values: 包含所有联盟价值的字典

    Returns:
        包含每个成员Shapley值的列表
    """
    # 计算每个成员的Shapley值
    shapley_values = []
    for player in range(n):
        shapley_value = 0

        for subset_size in range(n):
            # 计算权重
            weight = factorial(subset_size) * factorial(n - subset_size - 1) / factorial(n)

            # 遍历所有不包含该成员的子集
            for subset in combinations([p for p in range(n) if p != player], subset_size):
                # 计算边际贡献
                coalition_without_player = subset
                coalition_with_player = tuple(sorted(subset + (player,)))

                # 获取联盟价值
                value_without = coalition_values.get(coalition_without_player, 0)
                value_with = coalition_values.get(coalition_with_player, 0)

                # 计算边际贡献
                marginal_contribution = value_with - value_without

                # 更新Shapley值
                shapley_value += weight * marginal_contribution

        shapley_values.append(shapley_value)

    return shapley_values

def get_coalition_values_from_dict(n, input_dict):
    """
    从输入的字典中获取所有联盟的价值

    Args:
        n: 团队成员数量
        input_dict: 包含联盟价值的字典，格式如 {"empty_coalition":[0], "single_coalition":[1,2], ...}

    Returns:
        包含所有联盟价值的字典，格式为 {(成员1,成员2,...): 价值}
    """
    coalition_values = {}

    # 空联盟
    if "empty_coalition" in input_dict:
        coalition_values[tuple()] = input_dict["empty_coalition"][0]
    else:
        coalition_values[tuple()] = 0

    # 单人联盟
    if "single_coalition" in input_dict:
        values = input_dict["single_coalition"]
        for i in range(min(n, len(values))):
            coalition_values[(i,)] = values[i]

    # 双人联盟
    if "two_person_coalition" in input_dict:
        values = input_dict["two_person_coalition"]
        idx = 0
        for i, j in combinations(range(n), 2):
            if idx < len(values):
                coalition_values[(i, j)] = values[idx]
                idx += 1

    # 三人联盟
    if "three_person_coalition" in input_dict:
        values = input_dict["three_person_coalition"]
        idx = 0
        for i, j, k in combinations(range(n), 3):
            if idx < len(values):
                coalition_values[(i, j, k)] = values[idx]
                idx += 1

    # 四人联盟
    if "four_person_coalition" in input_dict:
        values = input_dict["four_person_coalition"]
        idx = 0
        for coalition in combinations(range(n), 4):
            if idx < len(values):
                coalition_values[coalition] = values[idx]
                idx += 1

    # 五人联盟
    if "five_person_coalition" in input_dict:
        values = input_dict["five_person_coalition"]
        idx = 0
        for coalition in combinations(range(n), 5):
            if idx < len(values):
                coalition_values[coalition] = values[idx]
                idx += 1

    # 六人联盟
    if "six_person_coalition" in input_dict:
        values = input_dict["six_person_coalition"]
        idx = 0
        for coalition in combinations(range(n), 6):
            if idx < len(values):
                coalition_values[coalition] = values[idx]
                idx += 1

    # 七人联盟
    if "seven_person_coalition" in input_dict:
        values = input_dict["seven_person_coalition"]
        idx = 0
        for coalition in combinations(range(n), 7):
            if idx < len(values):
                coalition_values[coalition] = values[idx]
                idx += 1

    # 更多人的联盟可以按需添加...

    # 全部n人联盟
    if f"{n}人联盟" in input_dict:
        values = input_dict[f"{n}人联盟"]
        if values:
            coalition_values[tuple(range(n))] = values[0]

    return coalition_values

def get_coalition_values(n):
    """
    获取所有可能联盟的价值

    Args:
        n: 团队成员数量

    Returns:
        包含所有联盟价值的字典
    """
    coalition_values = {}

    # 空联盟
    coalition_values[tuple()] = float(input(f"请输入空联盟的价值 (通常为0): "))

    # 获取所有其他联盟的价值
    for size in range(1, n+1):
        print(f"\n--- 输入大小为 {size} 的联盟价值 ---")
        for coalition in combinations(range(n), size):
            # 显示联盟成员 (从1开始计数)
            members = [i+1 for i in coalition]
            value = float(input(f"联盟 {members} 的价值: "))
            coalition_values[coalition] = value

    return coalition_values

# 主函数
if __name__ == "__main__":
    # 获取团队成员数量
    n = int(input("请输入团队成员数量: "))

    # 选择输入方式
    input_method = input("\n请选择输入方式 (1: 字典输入, 2: 逐个输入): ")

    if input_method == '1':
        # 字典输入方式
        print("\n请输入包含所有联盟价值的字典，格式如下:")
        print('{"empty_coalition":[0], "single_coalition":[值1,值2,...], "two_person_coalition":[值1,值2,...], ...}')
        print("例如，对于2人团队: {\"empty_coalition\":[0], \"single_coalition\":[10,20], \"two_person_coalition\":[50]}")

        try:
            input_dict = eval(input("\n输入字典: "))
            coalition_values = get_coalition_values_from_dict(n, input_dict)
        except Exception as e:
            print(f"输入错误: {e}")
            exit(1)
    else:
        # 逐个输入方式
        print(f"\n您需要输入所有可能联盟的价值 (共 {2**n - 1} 个非空联盟)")
        coalition_values = get_coalition_values(n)

    # 计算奖励分配
    reward_allocation = calculate_shapley_values(n, coalition_values)

    # 询问是否计算特定成员的Shapley值
    calc_specific = input("\n是否计算特定成员的Shapley值? (y/n): ").lower()

    if calc_specific == 'y':
        member = int(input(f"请输入成员编号 (1-{n}): ")) - 1
        if 0 <= member < n:
            print(f"\n成员 {member+1} 的Shapley值: {reward_allocation[member]}")
        else:
            print("无效的成员编号!")

    # 输出所有成员的奖励分配结果
    print("\n所有成员的奖励分配结果:")
    print(reward_allocation)
