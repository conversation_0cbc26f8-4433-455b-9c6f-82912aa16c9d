from datetime import datetime, timed<PERSON>ta

from django.http import JsonResponse
from django.shortcuts import render
from django.contrib.auth.models import User
from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate, logout

from .models import Usage
from .register_form import UserRegistrationForm
from django.contrib.auth.forms import AuthenticationForm
from django.db import IntegrityError
from style_content.tools.load_config import Config
import logging

logger = logging.getLogger(__name__)
config = Config()
model_price = config.get_config()["model_price"]
# Create your views here.
def login_user(request):
    if request.user.is_authenticated:
        return JsonResponse({"status": 1})
    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                return JsonResponse({"status":1})
    else:
        return JsonResponse({"status":0})
    return JsonResponse({"status":0})

def register(request):
    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            try:
                user = form.save()
                login(request, user)
            except IntegrityError as e:
                logger.error('register_database error: %s', str(e))
        else:
            if 'username' in form.errors:
                return JsonResponse({'status': -3})
            elif 'password' in form.errors:
                return JsonResponse({'status': -2})
            elif 'email' in form.errors:
                return JsonResponse({'status': -1})
            else:
                return JsonResponse({'status': 0})
    else:
        return JsonResponse({"status": 0})
    return JsonResponse({"status":1})

def log_out(request):
    logout(request)
    return JsonResponse({"status":1})

def get_usage(request):
    month = request.GET.get("month")
    year = request.GET.get("year")
    # 将字符串转换为整数
    month = int(month)
    year = int(year)

    # 获取当月的第一天
    first_day_of_month = datetime(year, month, 1)

    # 获取下个月的第一天
    if month == 12:
        first_day_of_next_month = datetime(year + 1, 1, 1)
    else:
        first_day_of_next_month = datetime(year, month + 1, 1)

    # 获取用户本月的使用情况
    usages_this_month = Usage.objects.filter(
        userid__id=request.user.id,
        use_time__gte=first_day_of_month,
        use_time__lte=first_day_of_next_month
    ).values("use_time", "use_model", "prompt_token", "output_token")
    context = {"data": list(usages_this_month), "price":model_price}
    for i in context["data"]:
        i["use_time"] = i["use_time"].day
    print(context)

    return render(request, "index/usage.html", context)

def find_usage(request):
    month = request.GET.get("month")
    year = request.GET.get("year")
    # 将字符串转换为整数
    month = int(month)
    year = int(year)

    # 获取当月的第一天
    first_day_of_month = datetime(year, month, 1)

    # 获取下个月的第一天
    if month == 12:
        first_day_of_next_month = datetime(year + 1, 1, 1)
    else:
        first_day_of_next_month = datetime(year, month + 1, 1)

    # 获取用户本月的使用情况
    usages_this_month = Usage.objects.filter(
        userid__id=request.user.id,
        use_time__gte=first_day_of_month,
        use_time__lte=first_day_of_next_month
    ).values("use_time", "use_model", "prompt_token", "output_token")
    context = {"data": list(usages_this_month), "price":model_price}
    for i in context["data"]:
        i["use_time"] = i["use_time"].day
    return JsonResponse(context)
# 添加新的接口用于检查登录状态

# def check_login_status(request):
#     # 获取当前用户是否登录
#     is_logged_in = request.user.is_authenticated
#     return render(request, 'checkCopywriting.html', {'isLoggedIn': is_logged_in})

def check_login_status(request):
    is_logged_in = request.user.is_authenticated
    logger.info(f"用户登录状态: {is_logged_in}")
    return JsonResponse({'isLoggedIn': is_logged_in})