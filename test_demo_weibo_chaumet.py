from IPython.utils import io
from serpapi import GoogleSearch
import os
import string
import re


import openai
import urllib.request, json

from langchain_openai import ChatOpenAI
from serpapi import GoogleSearch
from dotenv import load_dotenv
from IPython.utils import io
# from Multimodal_gemini import ImageAnalyzer



load_dotenv(r'D:\硕士\aiagent\gpt_CWSC\.env',override=True) # 关键：`override=True`

openai_api_key = os.getenv("OPENAI_API_KEY")
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY_2")
print(deepseek_api_key)
print(openai.api_key)


template = """你是微博和视频号平台文案编辑专家，请根据提供的产品资料、产品受众、产品卖点、文案主题和文案类型，充分理解并分析品牌的调性、产品的特点和卖点，为品牌撰写一则宣传文案。

## 根据提供的信息：产品名称是{Product_Information}、产品的受众人群是{Product_Audience}、产品的核心卖点(也是最重要的一点)是{Product_Selling_Points}以及文案主题是{Copy_Theme}，充分理解并分析品牌的调性、产品的特点和卖点，为{Product_Information}生成一条的广告文案。
#产品补充背景
{Additional_Resource}
你遵循的创作技能如下：
1. 第一句需要简短有内涵，具有强烈的吸引力和明确的主题导向。
2. 整体文案字数不超过120字。
3. 用词：符合品牌的调性，吸收示例的风格精髓，并结合品牌特色进行创新表达。文案语言需要保持生动优美，句式简洁、富有诗意。
4. 逻辑：逻辑清晰、前后衔接自然。
5. 内容限制：文案中需围绕所给产品的信息进行创意表达，禁止引入其他无关内容。
# 示例:
- 示例1：
当尚美蓝盒开启，情意，落于指间，爱在此刻，
无尽延续。以Joséphine约瑟芬系列白鹭冠冕钻
戒，见证爱的誓言。

前往CHAUMET尚美巴黎官方精品店http://t.cn/A6ggfZmF、天猫官方旗舰店http://t.cn/A6THBEyW，或线下高级精品店http://t.cn/A6NTxyXR，体验“Crown Your Love为爱加冕”钻戒定制服务，为爱意时刻璀璨加冕。
#CHAUMET尚美巴黎##自在风雅 加冕真我##尚美Josephine##CHAUMET蓝盒#
- 示例2：
以全新Bee de Chaumet系列手镯，感受不对称
设计的别致意趣。一对蜜蜂灵动起舞，钻石铺镶
其上，隔空相望的精巧造型，生动诠释
“TOI&MOI”主题。

前往CHAUMET尚美巴黎官方精品店http://t.cn/A6ePoffc、天猫官方旗舰店http://t.cn/A6THBEyW，或线下高级精品店http://t.cn/A6NTxyXR，探索蜂巢艺作。
#CHAUMET尚美巴黎##尚美蜂巢#
- 示例3：
紧密相连，心意相通。粉色、蓝色或彩虹色调的
迷人宝石，与钻石和谐交错。立体镂空设计，见
证融洽时分。

前往CHAUMET尚美巴黎官方精品店http://t.cn/A6eBbAQf、天猫官方旗舰店http://t.cn/A6THBEyW，或线下高级精品店http://t.cn/A6NTxyXR，开启连结时刻。
#CHAUMET尚美巴黎##尚美巴黎Liens缘系一生系列##尚美Liens# 
"""









llm_gpt4o = ChatOpenAI(model="gpt-4o", temperature=1.0,api_key = openai_api_key,base_url='https://xiaoai.plus/v1',top_p=0.7)

llm_deepseek = ChatOpenAI(
    model='deepseek-chat',
    openai_api_key=deepseek_api_key,
    openai_api_base='https://api.deepseek.com/v1',
    max_tokens=100,
    temperature=1.5
)
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate.from_template(template)
# 定义字典
prompt_dict = {
    "Product_Information": "CHAUMET《为爱加冕》婚尚专题系列",
    "Product_Audience": "有一定经济实力，追求高品质生活，重视爱情的仪式感与情感深度，并对具有悠久历史传承和卓越工艺的奢侈品牌抱有认同与向往的消费者。其中，中国的千禧一代和Z世代（特别是准婚族）是品牌积极拓展和沟通的重要群体。",
    "Product_Selling_Points": "将拥有CHAUMET《为爱加冕》戒指，视为一段源自皇室的、充满仪式感与极致美感的爱情加冕之旅，承载着永恒的承诺与最美好的祝愿。",
    "Copy_Theme": "Chaumet Crown Your Love (Chaumet 为爱加冕) - 这是核心主题",

}

# analyzer = ImageAnalyzer(
#         env_path=r'D:\硕士\aiagent\gpt_CWSC\.env',
#         model_name="gemini-2.0-flash",
#         temperature=0,
#         timeout=30
#     )
#
# print("\n" + "=" * 50)


# prompt1 = "使用中文描述这些图片的{}产品，字数大概300左右".format(prompt_dict['Product_Information'])
#
# print("\n=== 示例4: 分析多张本地图片（简化列表） ===")
# local_images = [
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\1.png",
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\2.png"  # 假设存在
#             ]
#
# result = analyzer.analyze_image(local_images, prompt=prompt1,is_local= True)
# if result["success"]:
#     print("分析结果:", result["result"])
#     print(f"处理图片数: {result['processed_images_count']}/{result['total_images_count']}")
# else:
#     print("分析失败:", result["error"])






#additional_data2 =  result["result"]
# additional_data2 = """这是一款来自 Bee de Chaumet 系列的精致项链，以其独特的蜜蜂造型吸引目光 。项链的主体是一只栩栩如生的黄金蜜蜂，其设计巧妙，细节之处彰显匠心 。蜜蜂的造型由黄金精妙打造，呈现出圆润而富有层次感的线条，仿佛蜜蜂在空中轻盈飞舞 。
#
#
# 项链的亮点在于蜜蜂的眼睛部分，镶嵌着明亮式切割钻石 。这些钻石不仅为蜜蜂增添了一丝灵动与光彩，更在黄金的衬托下显得熠熠生辉，使得整个设计更加生动传神 。蜜蜂的翅膀设计也十分考究，可能采用了抛光或拉丝工艺，以营造出不同的质感和光影效果，使其看起来更具立体感 。
#
#
# 除了蜜蜂本身，项链的链条也与蜜蜂造型完美融合，细致的黄金链条与主体相得益彰，既保证了佩戴的舒适性，又突出了蜜蜂的精致造型 。项链的整体设计传达出 Chaumet 品牌对自然生灵的赞美和精湛的珠宝工艺。蜜蜂在 Chaumet 品牌中也具有特殊的象征意义，代表着勤劳、忠诚和皇室的庇护，赋予了这款项链更深层次的寓意。
#
# 这款 Bee de Chaumet 项链不仅是一件珠宝，更是一件艺术品，完美融合了自然元素与奢华材质，适合日常佩戴或作为特殊场合的点睛之笔，展现佩戴者独特的品味与优雅气质。"""
# print(additional_data2)
additional_data2 = """为爱加冕 (Wèi Ài Jiā Miǎn) - “Crown Your Love”的中文翻译，直接唤起为爱加冕的意象，暗示着荣耀、尊贵和终极承诺。
No. 04 - 虽然不是直接文案，但它暗示着一个系列或集合，意味着有更多内容值得探索。
来自 image_2c1a65.png：

Chaumet Paris Crown Your Love Ring (Chaumet Paris 为爱加冕戒指) - 强调品牌产地和系列中的特定产品（戒指）。
戒指在盒中被打开的画面象征着惊喜、求婚、期待和珍宝的揭示。
来自 image_2c1a7d.png：

CHAUMET蓝盒 (CHAUMET Lán Hé) - CHAUMET蓝色盒子。这突出了其标志性的包装及其重要性。
承袭源自19世纪的设计灵感 (Chéngxí Yuánzì 19 Shìjì De Shèjì Línggǎn) - 强调了传承、永恒和设计背后的丰富历史。
启封之时，仿若教堂之门双向打开 (Qǐfēng Zhī Shí, Fǎngruò Jiàotáng Zhī Mén Shuāngxiàng Dǎkāi) - 这是一个极具诗意和感染力的短语，将开盒的动作与神圣、庄重和仪式性的时刻联系起来，如同教堂婚礼一般。它暗示着命运和新的开始。
美好祝愿，凝于一抹尚美蓝 (Měihǎo Zhùyuàn, Níng Yú Yī Mǒ Shàngměi Lán) - 将盒子的蓝色与美好的祝愿、祝福以及美丽和优雅的精髓联系起来（“尚美”直接翻译为“欣赏美丽”，常被用作“Chaumet”的优雅音译）。"""



prompt_dict['Additional_Resource'] = additional_data2

# 使用字典填充


text1 = """
角色：一名产品受众人群分析师。

指令：根据补充资料细化人类给出的的受众人群，字数大概在30字左右,只需返回最重要的结果即可，优化后的结果以双引号输出。

补充资料：{additional_data}

"""
#
Product_Audience_prompt = ChatPromptTemplate(
    [
        (
            "system",
            text1
        ),
        ("human", "{input}"),
    ]
)
prompt_dict['Product_Audience'] =llm_deepseek.invoke(Product_Audience_prompt.format(additional_data = additional_data2,input=prompt_dict['Product_Audience'])).content

prompt = prompt.format(**prompt_dict)
print(prompt)
res = llm_gpt4o.invoke(prompt)
print(res.content)


