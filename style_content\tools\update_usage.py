from datetime import date

from accounts.models import Usage


async def aupdate_usage(user, model, prompt_token, output_token):
    usage_record = await Usage.objects.filter(
        userid=user,
        use_time=date.today(),
        use_model=model,
    ).afirst()
    if usage_record:
        # 更新token数量
        usage_record.prompt_token += prompt_token
        usage_record.output_token += output_token
        await usage_record.asave()
    else:
        # 如果没有找到记录，创建一个新的
        new_usage = Usage(
            userid=user,
            use_time=date.today(),
            prompt_token=prompt_token,
            output_token=output_token,
            use_model=model
        )
        await new_usage.asave()