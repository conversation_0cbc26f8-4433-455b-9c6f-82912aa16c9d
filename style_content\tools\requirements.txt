aiohttp==3.9.3
aiosignal==1.3.1
aiostream==0.5.2
alembic==1.13.1
altair==5.2.0
amqp==5.2.0
annotated-types==0.6.0
asgiref==3.7.2
async-timeout==4.0.3
autobahn==23.6.2
Automat==22.10.0
backoff==2.2.1
bcrypt==4.1.2
beautifulsoup4==4.12.3
billiard==4.2.0
blinker==1.7.0
bs4==0.0.2
cachetools==5.3.2
celery==5.3.6
certifi==2023.11.17
channels==4.0.0
chardet==5.2.0
charset-normalizer==3.3.2
chromedriver-autoinstaller==0.6.4
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cohere==4.47
comm==0.2.1
constantly==23.10.4
contourpy==1.2.0
courlan==1.0.0
cryptography==42.0.1
cssselect==1.2.0
cycler==0.12.1
daphne==4.0.0
dataclasses-json==0.5.14
datasets==2.17.0
dateparser==1.2.0
Deprecated==1.2.14
dill==0.3.8
dirtyjson==1.0.8
distro==1.9.0
Django==5.0.1
docopt==0.6.2
entrypoints==0.4
faiss-cpu==1.7.4
Faker==23.2.1
fastapi==0.109.0
fastavro==1.9.4
favicon==0.7.0
feedfinder2==0.0.4
feedparser==6.0.11
filelock==3.13.1
fonttools==4.49.0
frozendict==2.4.0
frozenlist==1.4.1
fsspec==2023.10.0
gitdb==4.0.11
GitPython==3.1.42
greenlet==3.0.3
h11==0.14.0
htbuilder==0.6.2
html2text==2020.1.16
htmldate==1.7.0
httpcore==1.0.2
httpx==0.26.0
httpx-sse==0.4.0
huggingface-hub==0.20.3
humanize==4.9.0
hyperlink==21.0.0
importlib-metadata==6.11.0
incremental==22.10.0
ipywidgets==8.1.2
jieba3k==0.35.1
joblib==1.3.2
jsonpatch==1.33
jsonpointer==2.4
jsonschema-specifications==2023.12.1
jupyterlab_widgets==3.0.10
jusText==3.0.0
kaggle==1.6.6
kiwisolver==1.4.5
kombu==5.3.6
langchain==0.1.4
langchain-community==0.0.16
langchain-core==0.1.17
langchain-openai==0.0.5
langchainhub==0.1.14
langcodes==3.3.0
langserve==0.0.41
langsmith==0.0.84
llama-index==0.10.34
llama-index-agent-openai==0.2.3
llama-index-cli==0.1.12
llama-index-core==0.10.34
llama-index-embeddings-huggingface==0.1.1
llama-index-embeddings-openai==0.1.9
llama-index-indices-managed-llama-cloud==0.1.6
llama-index-legacy==0.9.48
llama-index-llms-ollama==0.1.1
llama-index-llms-openai==0.1.16
llama-index-multi-modal-llms-openai==0.1.5
llama-index-postprocessor-cohere-rerank==0.1.1
llama-index-program-openai==0.1.6
llama-index-question-gen-openai==0.1.3
llama-index-readers-file==0.1.20
llama-index-readers-llama-parse==0.1.4
llama-index-readers-web==0.1.5
llama-parse==0.4.2
llamaindex-py-client==0.1.19
lxml==5.1.0
Mako==1.3.2
Markdown==3.5.2
markdown-it-py==3.0.0
markdownlit==0.0.7
marshmallow==3.20.2
matplotlib==3.8.3
mdurl==0.1.2
merkle-json==1.0.0
millify==0.1.1
more-itertools==10.2.0
mpmath==1.3.0
multidict==6.0.4
multiprocess==0.70.16
munch==4.0.0
mypy-extensions==1.0.0
mysqlclient==2.2.1
nest-asyncio==1.6.0
networkx==3.2.1
newspaper3k==0.2.8
nltk==3.8.1
numpy==1.26.3
openai==1.26.0
orjson==3.9.12
outcome==1.3.0.post0
packaging==23.2
pandas==2.2.0
pdf2image==1.17.0
pillow==10.2.0
pinecone-client==3.0.3
pipreqs==0.4.13
playwright==1.41.2
ply==3.11
protobuf==4.25.3
pyarrow==15.0.0
pyarrow-hotfix==0.6
pyasn1==0.5.1
pyasn1-modules==0.3.0
pydantic==2.6.1
pydantic_core==2.16.2
pydeck==0.8.1b0
pyee==11.0.1
pymdown-extensions==10.7
PyMuPDF==1.23.22
PyMuPDFb==1.23.22
pyOpenSSL==24.0.0
pyparsing==3.1.1
pypdf==4.0.1
PyQt5==5.15.10
pytesseract==0.3.10
python-decouple==3.8
python-docx==1.1.2
python-dotenv==1.0.1
python-slugify==8.0.4
pywin32==305.1
redis==5.0.3
referencing==0.33.0
regex==2023.12.25
requests-file==2.0.0
rich==13.7.0
rq==1.16.1
safetensors==0.4.2
scikit-learn==1.4.1.post1
scipy==1.12.0
selenium==4.17.2
sentence-transformers==2.3.1
sentencepiece==0.1.99
service-identity==24.1.0
sgmllib3k==1.0.0
slack-bolt==1.18.1
slack_sdk==3.27.0
smmap==5.0.1
sortedcontainers==2.4.0
SQLAlchemy==2.0.24
sqlparse==0.4.4
sse-starlette==1.8.2
st-annotated-text==4.0.1
starlette==0.35.1
streamlit==1.31.1
streamlit-aggrid==0.3.4.post3
streamlit-camera-input-live==0.2.0
streamlit-card==1.0.0
streamlit-embedcode==0.1.2
streamlit-extras==0.4.0
streamlit-faker==0.0.3
streamlit-image-coordinates==0.1.6
streamlit-javascript==0.1.5
streamlit-keyup==0.2.3
streamlit-toggle-switch==1.0.2
streamlit-vertical-slider==2.5.5
striprtf==0.0.26
sympy==1.12
tenacity==8.2.3
text-unidecode==1.3
threadpoolctl==3.3.0
tiktoken==0.5.2
tinysegmenter==0.3
tld==0.13
tldextract==5.1.1
tokenizers==0.15.2
toml==0.10.2
toolz==0.12.1
tqdm==4.66.1
trafilatura==1.7.0
transformers==4.37.2
trio==0.24.0
trio-websocket==0.11.1
trulens-eval==0.23.0
Twisted==23.10.0
twisted-iocpsupport==1.0.4
txaio==23.1.1
types-protobuf==4.24.0.20240129
types-requests==2.31.0.20240125
typing-inspect==0.8.0
typing_extensions==4.9.0
tzdata==2023.4
tzlocal==5.2
urllib3==2.0.0
uvicorn==0.27.0.post1
validators==0.22.0
vine==5.1.0
watchdog==4.0.0
webencodings==0.5.1
widgetsnbextension==4.0.10
wrapt==1.16.0
wsproto==1.2.0
xxhash==3.4.1
yarg==0.1.9
yarl==1.9.4
zipp==3.17.0
zope.interface==6.1
