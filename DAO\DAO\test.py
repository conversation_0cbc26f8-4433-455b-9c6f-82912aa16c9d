import json
from typing import Dict, List, Tuple, Set
from dataclasses import dataclass


@dataclass
class TextUnit:
    id: str
    content: str
    type: str = "text"


@dataclass
class SemanticUnit:
    id: str
    content: str
    type: str = "semantic_unit"


@dataclass
class Entity:
    id: str
    content: str
    type: str = "entity"


@dataclass
class Relationship:
    id: str
    content: str
    type: str = "relationship"


class SimpleProcessor:
    def __init__(self):
        self.T: List[TextUnit] = []
        self.S: List[SemanticUnit] = []
        self.N: List[Entity] = []
        self.R: List[Relationship] = []

    def process_json_text(self, json_text: str, auto_deduplicate=True):
        """
        处理JSON文本，创建文本单元、语义单元、实体和关系

        参数:
            json_text: JSON格式的文本
            auto_deduplicate: 是否自动去重实体，默认为True
        """
        # 解析JSON
        data = json.loads(json_text)

        # 创建文本单元T
        self.T.append(TextUnit(
            id="T1",
            content=json_text
        ))

        # 处理每个语义单元
        for i, unit in enumerate(data):
            # 创建语义单元S
            self.S.append(SemanticUnit(
                id=f"S{i}",
                content=unit["semantic_unit"]
            ))

            # 创建实体N
            for entity in unit["entities"]:
                self.N.append(Entity(
                    id=f"N_{entity}",
                    content=entity
                ))

            # 创建关系R
            for j, relation in enumerate(unit["relationships"]):
                self.R.append(Relationship(
                    id=f"R{i}_{j}",
                    content=relation
                ))

        # 如果启用自动去重，处理完所有数据后去重实体
        if auto_deduplicate:
            self.deduplicate_entities()

    def print_stats(self):
        print(f"Text units (T): {len(self.T)}")
        print(f"Semantic units (S): {len(self.S)}")
        print(f"Entities (N): {len(self.N)}")
        print(f"Relationships (R): {len(self.R)}")

    def print_details(self):
        print("\nText units (T):")
        for t in self.T:
            print(f"ID: {t.id}")

        print("\nSemantic units (S):")
        for s in self.S:
            print(f"ID: {s.id}")
            print(f"Content: {s.content}")
            print("-" * 80)

        print("\nEntities (N):")
        for n in self.N:
            print(f"ID: {n.id}, Content: {n.content}")

        print("\nRelationships (R):")
        for r in self.R:
            print(f"ID: {r.id}, Content: {r.content}")

    def deduplicate_entities(self):
        """
        去重实体并保留所有关系
        """
        # 记录原始实体数量
        original_count = len(self.N)

        # 创建内容到实体ID的映射
        content_to_id = {}
        unique_entities = []

        # 第一步：创建唯一实体列表和映射
        for entity in self.N:
            if entity.content not in content_to_id:
                # 为每个唯一内容创建一个新的实体ID
                new_id = f"N_{entity.content}"
                content_to_id[entity.content] = new_id

                # 添加到唯一实体列表
                unique_entities.append(Entity(
                    id=new_id,
                    content=entity.content
                ))

        # 用唯一实体列表替换原始列表
        self.N = unique_entities

        # 打印去重统计信息
        print(f"\n实体去重统计:")
        print(f"原始实体数量: {original_count}")
        print(f"去重后实体数量: {len(self.N)}")
        print(f"减少了 {original_count - len(self.N)} 个重复实体")

    def get_entity_by_content(self, content: str) -> Entity:
        """
        根据实体内容获取实体对象

        参数:
            content: 实体内容

        返回:
            Entity对象，如果找不到则返回None
        """
        for entity in self.N:
            if entity.content == content:
                return entity
        return None

    def get_entity_relationships(self, entity_content: str) -> List[Relationship]:
        """
        获取与特定实体相关的所有关系

        参数:
            entity_content: 实体内容

        返回:
            包含该实体的所有关系列表
        """
        return [rel for rel in self.R if entity_content in rel.content]

    def get_entity_semantic_units(self, entity_content: str) -> List[SemanticUnit]:
        """
        获取包含特定实体的所有语义单元

        参数:
            entity_content: 实体内容

        返回:
            包含该实体的所有语义单元列表
        """
        return [sem for sem in self.S if entity_content in sem.content]

    def analyze_entity(self, entity_content: str) -> str:
        """
        分析单个实体的所有相关信息

        参数:
            entity_content: 实体内容

        返回:
            包含实体分析信息的字符串
        """
        relationships = self.get_entity_relationships(entity_content)
        semantic_units = self.get_entity_semantic_units(entity_content)

        return f"""
Entity: {entity_content}

Related Relationships ({len(relationships)}):
{chr(10).join(f"- {r.content}" for r in relationships)}

Semantic Context ({len(semantic_units)}):
{chr(10).join(f"- {s.content}" for s in semantic_units)}
"""


# 使用示例
# processor = SimpleProcessor()
# your_json_text = """[{"semantic_unit": "The bullish ETH trading summary highlights favorable conditions such as potential Fed rate cuts in 2024, Bitcoin ETF approvals boosting crypto sentiment, Ethereum's EIP-4844 upgrade reducing L2 costs, rising staking demand, and fee-burning mechanisms making ETH deflationary.", "entities": ["ETH", "2024", "FED", "BITCOIN ETF", "EIP-4844", "L2", "STAKING DEMAND", "FEE-BURNING MECHANISM"], "relationships": ["FED, potential rate cuts in, 2024", "BITCOIN ETF, approvals boosting, CRYPTO MARKET SENTIMENT", "EIP-4844, expected in, 2024", "EIP-4844, reducing costs for, L2", "STAKING DEMAND, rising with, ETH", "FEE-BURNING MECHANISM, making, ETH deflationary"]}, {"semantic_unit": "ETH shows strong on-chain and technicals, including a rising ETH/BTC ratio, holding key support levels, and growing institutional interest such as BlackRock's Ethereum ETF application.", "entities": ["ETH", "ETH/BTC RATIO", "KEY SUPPORT LEVELS", "BLACKROCK", "ETHEREUM ETF"], "relationships": ["ETH/BTC RATIO, showing strength for, ETH", "ETH, holding, KEY SUPPORT LEVELS", "BLACKROCK, applied for, ETHEREUM ETF"]}, {"semantic_unit": "Upcoming catalysts for ETH include Spot Ethereum ETF decisions in May 2024 and Layer-2 ecosystem growth with platforms like Arbitrum, Optimism, and zkSync driving utility.", "entities": ["ETH", "SPOT ETHEREUM ETF", "2024-05", "LAYER-2 ECOSYSTEM", "ARBITRUM", "OPTIMISM", "ZKSYNC"], "relationships": ["SPOT ETHEREUM ETF, decisions expected in, 2024-05", "LAYER-2 ECOSYSTEM, growth driven by, ARBITRUM, OPTIMISM, ZKSYNC"]}, {"semantic_unit": "The proposed trading action suggests accumulating ETH at key support levels ($3,200\u2013$3,400), using DCA, and optionally leveraging with tight stop-losses. Targets include $3,800\u2013$4,000 short-term and $4,500\u2013$5,000 mid-term.", "entities": ["ETH", "KEY SUPPORT ZONE", "DCA", "LEVERAGE", "STOP-LOSSES", "TARGETS"], "relationships": ["ETH, accumulate in, KEY SUPPORT ZONE ($3,200\u2013$3,400)", "DCA, used to mitigate, VOLATILITY RISK", "LEVERAGE, optional with, STOP-LOSSES (~$3,000)", "ETH, targets for, TARGETS ($3,800\u2013$5,000)"]}, {"semantic_unit": "Risk management includes monitoring Bitcoin dominance and regulatory FUD. Long-term strategies involve staking ETH for APY and hedging with puts if volatility spikes.", "entities": ["RISK MANAGEMENT", "BITCOIN DOMINANCE", "REGULATORY FUD", "ETH", "STAKING", "APY", "HEDGING", "PUTS", "VOLATILITY"], "relationships": ["RISK MANAGEMENT, includes monitoring, BITCOIN DOMINANCE", "REGULATORY FUD, watch for, ETH", "ETH, staking for, APY", "HEDGING, using, PUTS for VOLATILITY"]}, {"semantic_unit": "The conclusion advises buying ETH on weakness, scaling into strength, and trailing stops above $3,500, given ETH's strong fundamentals and upcoming catalysts.", "entities": ["ETH", "BUY WEAKNESS", "SCALE INTO STRENGTH", "TRAILING STOPS", "$3,500"], "relationships": ["ETH, strategy to, BUY WEAKNESS", "ETH, scale into, STRENGTH", "TRAILING STOPS, set above, $3,500"]}]
# """
# processor.process_json_text(your_json_text)  # your_json_text 是你提供的JSON字符串
# processor.print_stats()
# processor.print_details()