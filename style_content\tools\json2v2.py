import json
import re

# 定义文件路径
file_path = './kuaishouDouyinv2.jsonl'
OA_prompt = """现在你是一名文案撰写专家，首先你需要分析示例文案的风格，然后通过给定的文案信息撰写文案。
<文案要求>
1.文案每句话都需要占用单独一行，并且保持简短。
2.风格与示例文案保持一致，需要发挥创造力。
3.使用用户给定的文案信息，若缺少相关信息，可发挥想象力。
4.语言保持生动优美，运用情感渲染、故事叙述、形象描绘、色彩表达、情感连接等方式介绍产品。
5.正文采用分段的方法，每个段落突出不同的功能或特点，但要避免过于直白的表达。并且从感性和理性两方面阐述产品的优势。
6.输出标题、摘要、正文三部分即可。
</文案要求>
<示例文案>
**1.标题：新品上市 | 和 SALOMON 溯溪鞋一起淌水入夏
摘要：穿上溯溪鞋，把夏天淌进水里
正文：初夏来袭，城市户外地图更新中
水上玩法即将解锁
季节装备，正在等待焕新……

SALOMON 溯溪鞋
助力自在踏水，一起把夏天淌进水里
专业溯溪/ TECHAMPHIBIAN 5

◎点击解锁专业淌水视角
专业实力，为涉水而生
自在探索多元场景
让户外更加安全有趣
体验这场丰富的夏季之旅

-溯溪科技-
搭载 SensiFIT™ 科技
从中底到鞋带，为双脚提供包裹与支撑
配合加固加厚鞋头
降低溯溪过程中的石块撞击风险
专业 Water Contagrip® 粘性大底
踏上湿滑地形
依旧稳定抓地，实现有效防滑

轻薄帆布拼接防碎石细孔网布
有效加速水分蒸发，实现快速干燥
同时防止水中泥土掉落鞋内

快乐踩水/ TECHSONIC
皮质材料打造摩登之感
透气设计为凉鞋开辟全新领域
户外溯溪or城市踏水，舒适随时相伴
-踏水守护-
采用 quickLACE 快速系带系统
在短时间内迅速完成系带动作
搭配SensiFIT™设计
有效减轻脚面压力，自在畅快踩水

可折叠鞋跟设计
满足户外和城市穿着的双重需求
场景转变，随时应变

夏季地图更新完成
户外水域成功解锁
穿上 SALOMON 溯溪鞋款
一起淌水出发，迎接初夏到来
全国门店现已上市

**2.标题：低温前行，攀登无界
摘要：2023户外秋冬徒步系列全新登场
正文：地势变幻，不抵内心探索渴望
看见低温预警 向冬日山脉，发起新的挑战 

◎愈低温，愈徒步前行
SALOMON 2023全新户外秋冬徒步系列

逆风攀登，纵览山巅严寒光景
享受季节的反差感
看见一处与众不同的罕见风景

山野探寻 温暖随行

PATROLLER GORE-TEX 羽绒服
雨雪纷飞的季节，山地活动也未曾停歇
达到RDS认证的700蓬松度羽绒
配合双层透气的GORE-TEX面料
兼具强大保暖与防水性能
高领设计协同符合人体工程学的剪裁
为每一次穿着，提供舒适体验

PATROLLER 三合一羽绒服
寒冷潮湿的冬季，依旧与干爽相伴远行
外层使用防水面料
确保冬季环境下的干燥与温暖体验
羽绒服和外壳通过拉链链接
适配多元天气，实现三种穿着方式
极大程度满足多样户外场景 

ELIXIR ULTRA 羽绒大衣
与低温一同前行的，从来不是厚重与臃肿
整体采用大型羽绒腔室，羽绒占比高达90%
实现高性能的冬季保暖与防护功效
采取先进HeiQ XReflex热反射技术
增加超薄辐射隔热层的同时，不增加衣物自身重量
兼顾穿着体感与优越的保暖效果

徒步攀登 舒适向上
ELIXIR MID GORE-TEX
征服长距离，户外世界的每一处都是舞台
鞋面采用防水GORE-TEX薄膜
恰逢冬日雨水，一样无惧前行
Sensifit™中足包裹技术结合ActiveChassis™ 科技
为足部带来柔软的缓震效果与稳定支撑
R.Camber™ 弹性厚底特性
可在徒步时实现快速过渡
每一步的顺畅姿态，都将助你走得更远

追随寒潮降临
2023全新户外秋冬徒步系列
现已于SALOMON线上、线下门店同步上市

**3.标题：唤山者系列 | 拨开云雾，遇见墨色括苍
摘要：水墨山峦，尽写诗意
正文：眼前雾气盘绕
是山脉有形的呼吸

◎拨开云雾——
从中国户外名山汲取力量和灵感
打造Salomon首个本土特别企划
「唤山者」系列重磅压轴款
XT-QUEST括苍山
以中式水墨美学，勾勒临海括苍山磅礴之势
双脚踏访云端，传递山的呼唤

越上山巅 纵览诗意
作为Salomon赞助的 “柴古唐斯越野赛”的起源地
临海括苍山一直是越野跑者心中的“白月光”
也是Salomon首个中国限定「唤山者」系列的收官压轴之山

括苍临海，空蒙缥缈
漫步云端的诗境感，在中式水墨美学的步履间得到完整诠释
黑白交融犹如墨色入水
于鞋面肆意挥洒，诠释括苍山云雾缥缈的画中诗意

为致敬柴古唐斯2023年新赛季
XT-QUEST括苍山 作为「唤山者」压轴鞋款
将于10月28日线下同步发售

笔触细腻 勾勒自然
鞋款整体黑白交叠，似一幅水墨丹青图卷
诗意般诠释着括苍山自然风貌
虚实相交，传递山之呼唤
行进山中，感受在云雾间攀登的呼吸感

以传统水墨装饰鞋盒外观
渐变色调与云雾缭绕的括苍山遥相呼应
山气氤氲，似将倾盒而出

科技加持 惬意探山
搭载专业登山徒步鞋专用的4D ADVANCED CHASSIS™科技大底
借助其动态支撑与缓冲性能
为运动过程中的每一步，提供稳定可靠的过渡

SALOMON XT-QUEST括苍山
10月20日-10月27日
SALOMON官方小程序抽签通道开启
即刻前往，感悟括苍诗意
</示例文案>
<产品信息>{productinfo}</产品信息>
"""
WC_prompt ="""你是一位文案撰写专家，现在你需要为产品撰写简短的宣传文案。文案不能过长，可以使用包括但不限于情感渲染、故事叙述、形象描绘、色彩表达、情感连接等方式来撰写。文案语言需要保持生动优美，句式简洁、富有诗意。文案中不能出现所给产品信息之外的内容。文案第一句应简短有内涵，类似于标题的作用。尾句应邀请读者前往官网。文案字数不能超过100字。
**示例文案1：跟随旅行颂歌，开启夏日活力冒险。路易威登 Flight Mode 系列融汇复古风格旅行印章与现代简约造型，再现品牌经典元素。全新精致手袋和典雅套装，成就摩登出行的个性风范。欢迎登陆官网，赴约盎然旅程。
**示例文案2：几何线条转动视觉魔法，#CHAUMET BEE MY LOVE爱·巢 系列戒指，以镜面抛光黄金独自闪耀，或间隔铺镶或全镶嵌明亮式切割钻石，尽现自我棱角。即刻前往 CHAUMET线下高级精品店，或探索CHAUMET线上官方旗舰店以及天猫官方旗舰店，共赏风格艺作，解锁多重魅力。
**示例文案3：#HowDoYouSayJetAime #CHAUMET爱的表达式 为闪耀夺目的TA，探寻恰合心意的情人节礼物。Joséphine约瑟芬皇后系列钻戒，“V”形冠冕标志再现白鹭冠羽的纯净与灵动，予所爱之人最真切的深情告白。替你表达以爱相邀，共赴浪漫的甜蜜。即刻前往 CHAUMET线下高级精品店，或探索CHAUMET线上官方旗舰店以及天猫官方旗舰店，以礼为名，探寻爱的多种表达。
<产品信息>
{productinfo}</产品信息>"""

DY_prompt = """你是一位文案撰写专家，擅长分析及撰写各种风格的文案。
<任务>
请你根据所给的产品信息，为产品撰写宣传文案。文案的风格简洁优雅，具有画面感和情境代入感。标题围绕特定的主题展开，利用短句表达出产品希望塑造出的生活方式。

<创作要求>
1.你的回答只需要包括两部分，分别是文案的标题和置顶评论。
2.标题保持简短，15个字以内。
3.文案撰写需要注重引发读者情感共鸣和生活方式塑造。
4.置顶评论需要包含两句话，第一句话描述产品的特点和背后的理念。第二句话邀请读者前往官网。

<示例文案>
**示例文案1：标题：焕新旅途，夏日造型拍档已上线！置顶评论：路易威登 Flight Mode 系列沿袭经典制箱工艺，呈演春季探险旅途。欢迎登陆官网，探索更多心仪单品。
**示例文案2：标题：漫步海滩，轻嗅自由之息。置顶评论：路易威登追梦（Attrape-Rêves）女士香水交融可可与牡丹花的馥郁气息，带来迷人芬芳。邀您前往新品体验店，探索更多芳香佳作。
**示例文案3：标题：跟随浪花节拍，奔赴自由海岸。置顶评论：随风起航，赴往路易威登 2024 早秋男士系列的航海旅程。邀您登陆官网，揭晓新季潮流理念。"""

Red_prompt = """你是我的小红书写作助理，我会输入主题，你的任务是帮我生成一篇关于该主题的适用于小红书的种草笔记。笔记需包含引人入胜的标题和分段明确的正文。正文应该在五句话以内，并保持简短优雅，避免直白普通或拗口的再造词汇。在创作内容时，请参考以下示例内容以保证风格的一致性：
```
示例内容：
标题：逐光之影，深入沉香秘境
正文：
🌄风沙回荡，一缕沉香随光影穿梭而来。
🔮路易威登“浮影” Ombre Nomade 香氛宛如沙漠热风中飘散的缕缕轻烟，蕴藏神秘香息。
🏜️调香大师 Jacques Cavallier Belletrud 将沉香、安息香和覆盆子的微妙气息相融合，打造流动的感官体验。
```
在此基础上，请生成一篇符合小红书风格的笔记，确保标题和内容的紧凑性、语言的简洁优雅以及排版的清晰度。在此基础上，请生成一篇符合小红书风格的笔记，确保标题和内容的紧凑性、语言的简洁优雅以及排版的清晰度。具体要求如下：
{productinfo}
"""

prompt = "You are a helpful assistant"
# 打开并读取文件
with open(file_path, 'r', encoding='utf-8') as file:
    count = 0
    for line in file:
        # 去除行末的换行符
        line = line.strip()
        # 解析JSON对象
        json_object = json.loads(line)
        b1 = re.search(r"为(.*?)撰写", json_object["messages"][1]["content"]).group(1)
        p1 = re.search("<产品信息>(.*?)</产品信息>", json_object["messages"][1]["content"],re.DOTALL).group(1)
        # p2 = re.search(r"文案发布在(.*?)平台", json_object["messages"][1]["content"]).group(1)
        temp = ""
        p2 = "快手"
        if p2 == "快手" or p2 == "抖音":
            temp = {"messages": [{"role": "system", "content": DY_prompt}, {"role": "user", "content": p1}, {"role": "assistant", "content": json_object["messages"][2]["content"]}]}
        elif p2 == "公众号":
            temp = {"messages": [{"role": "system", "content": prompt},
                                 {"role": "user", "content": OA_prompt.replace("{productinfo}", p1)},
                                 {"role": "assistant", "content": json_object["messages"][2]["content"]}]}


        elif p2 == "视频号" or p2 == "微博" or p2 == "微信":
            temp = {"messages": [{"role": "system", "content": prompt}, {"role": "user", "content": WC_prompt.replace("{productinfo}",p1)}, {"role": "assistant", "content": json_object["messages"][2]["content"]}]}
        elif p2 == "小红书":
            temp = {"messages": [{"role": "system", "content": prompt},
                                 {"role": "user", "content": Red_prompt.replace("{productinfo}", p1)},
                                 {"role": "assistant", "content": json_object["messages"][2]["content"]}]}

        print(temp)
        # 处理解析后的JSON对象
        with open("kuaishou.jsonl","a",encoding="utf-8") as f:
            f.write(json.dumps(temp, ensure_ascii=False) + "\n")