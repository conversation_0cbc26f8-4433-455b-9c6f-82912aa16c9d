import tkinter as tk
from openai import OpenAI
import json
import threading
import re

# 精心设计的地图，保证所有地点可达
# 0 表示街道（可通行），1 表示商铺（不可通行）
# 外层列表索引是行，内层列表索引是列
MAP = [
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 1, 0, 1, 0, 0],
    [0, 1, 0, 0, 0, 1, 0, 0, 0, 0],
    [0, 0, 0, 1, 0, 0, 0, 0, 0, 1],
    [0, 1, 0, 1, 0, 1, 0, 1, 0, 1],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
    [0, 1, 0, 0, 0, 1, 1, 1, 0, 1],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 1],
    [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 1, 1, 1, 1, 1, 0, 1, 0, 0]
]

# 每个格子的大小
TILE_SIZE = 50

# 定义 10 个地点，确保每个地点在商铺门口（街道上）
# 这里 x 表示列，y 表示行
DESTINATIONS = {
    "广场": (0, 0),
    "超市": (2, 3),
    "医院": (3, 7),
    "学校": (9, 1),
    "公园": (0, 4),
    "图书馆": (4, 2),
    "餐厅": (8, 3),
    "警局": (1, 7),
    "工厂": (6, 7),
    "商场": (9, 9)
}

# 角色移动速度
SPEED = 109

class Character:
    def __init__(self, canvas, x, y):
        self.canvas = canvas
        self.x = x
        self.y = y
        self.id = canvas.create_rectangle(
            self.x * TILE_SIZE, self.y * TILE_SIZE,
            (self.x + 1) * TILE_SIZE, (self.y + 1) * TILE_SIZE,
            fill="blue"
        )
        self.path = []
        self.is_moving = False
        self.path_line = None
        self.coord_label = canvas.create_text(
            (self.x + 0.5) * TILE_SIZE, (self.y + 0.5) * TILE_SIZE,
            text=f"({self.x}, {self.y})",
            fill="white"
        )

    def move(self, dx, dy):
        new_x = self.x + dx
        new_y = self.y + dy

        # 检查是否越界
        if new_x < 0 or new_x >= len(MAP[0]) or new_y < 0 or new_y >= len(MAP):
            return

        # 检查是否撞到障碍物
        if MAP[new_y][new_x] == 1:
            return

        self.x = new_x
        self.y = new_y
        self.canvas.move(self.id, dx * TILE_SIZE, dy * TILE_SIZE)
        self.canvas.move(self.coord_label, dx * TILE_SIZE, dy * TILE_SIZE)
        self.canvas.itemconfig(self.coord_label, text=f"({self.x}, {self.y})")

    def follow_path(self):
        if self.path:
            next_x, next_y = self.path.pop(0)
            dx = next_x - self.x
            dy = next_y - self.y
            if abs(dx) > SPEED:
                dx = SPEED if dx > 0 else -SPEED
            if abs(dy) > SPEED:
                dy = SPEED if dy > 0 else -SPEED
            self.move(dx, dy)
            self.canvas.after(20, self.follow_path)
        else:
            self.is_moving = False

    def draw_path(self, path):
        if self.path_line:
            self.canvas.delete(self.path_line)
        points = []
        for point in path:
            x, y = point
            x_pixel = x * TILE_SIZE + TILE_SIZE / 2
            y_pixel = y * TILE_SIZE + TILE_SIZE / 2
            points.extend([x_pixel, y_pixel])
        self.path_line = self.canvas.create_line(points, fill="red", width=2)

    def get_coordinate(self):
        return (self.x, self.y)


# 调用 LLM 进行路径规划
def llm_path_planning(start, question, max_retries=3):
    retries = 0
    messages = []
    system_message = {
        "role": "system",
        "content": "你是一个专业的路径规划师，根据给定的地图和起点，规划出避开障碍物的有效路径。回复必须是有效的 JSON 格式的坐标列表，如 [[x1, y1], [x2, y2], ...]。"
    }
    map_str = "\n".join([str(row) for row in MAP])
    start_str = str(start)
    user_message = {
        "role": "user",
        "content": f"障碍物的分布：\n{map_str}\n在这个地图中，数字 0 表示可通行的街道区域，数字 1 表示不可通行的商铺区域（坐标从 0 开始）。这里需要注意，地图使用二维数组表示，数组的外层索引对应地图的行，内层索引对应地图的列。也就是说，MAP[y][x] 表示地图中第 y 行第 x 列的位置。\n地点信息：\n{DESTINATIONS}\n用户起点：\n{start_str}\n用户问题：\n{question}\n请直接以 JSON 格式的坐标列表形式给出路径规划结果，例如: [[x1, y1], [x2, y2], ...]，仅考虑上下左右移动。在生成路径后，必须对路径进行严格自查，具体步骤如下：\n1. 依次取出路径中的每个坐标 (x, y)。\n2. 检查地图中第 y 行第 x 列的值是否为 0。\n3. 如果所有坐标对应的值都为 0，则路径有效；若存在值为 1 的情况，必须重新规划路径，确保路径完全避开值为 1 的不可通行障碍物。若规划的路径包含值为 1 的区域，会导致路径无效，必须重新规划。"
    }
    messages.append(system_message)
    messages.append(user_message)

    while retries < max_retries:
        try:
            client = OpenAI(
                api_key="748f58a2-2470-4feb-9168-cab7e4be9126",
                base_url="https://ark.cn-beijing.volces.com/api/v3"
            )

            print("发送给 LLM 的消息：", messages)
            completion = client.chat.completions.create(
                model="doubao-1-5-lite-32k-250115",
                temperature=0.6,
                messages=messages,
                stream=True
            )
            full_response = ""
            for chunk in completion:
                if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content:
                    full_response += chunk.choices[0].delta.content

            print("LLM 的回答：", full_response)

            # 调整正则表达式，更灵活地匹配JSON数据
            json_pattern = r'\[(\[[-+]?\d+,\s*[-+]?\d+\](,\s*\[[-+]?\d+,\s*[-+]?\d+\])*)\]'
            match = re.search(json_pattern, full_response)
            if match:
                json_content = match.group(0)
                try:
                    path = json.loads(json_content)
                    # 本地检查路径
                    if is_valid_path(path):
                        return path
                    else:
                        # 将 moderator 角色的消息转换为 user 角色的消息
                        invalid_points = [(x, y) for x, y in path if x < 0 or x >= len(MAP[0]) or y < 0 or y >= len(MAP) or MAP[y][x] == 1]
                        new_user_message = {
                            "role": "user",
                            "content": f"你之前提供的路径无效，包含值为 1 的区域或越界的坐标。无效坐标为：{invalid_points}。请重新规划路径，确保路径完全避开值为 1 的不可通行障碍物和越界情况。使用广度优先搜索（BFS）算法，回复必须是有效的 JSON 格式的坐标列表，如 [[x1, y1], [x2, y2], ...]。"
                        }
                        messages.append({"role": "assistant", "content": full_response})
                        messages.append(new_user_message)
                        print("LLM 生成的路径无效，包含值为 1 的区域或越界情况。尝试重新规划...")
                except json.JSONDecodeError:
                    print("解析 LLM 路径规划结果失败，完整响应内容如下：")
                    print(json_content)
            else:
                print("未找到有效的 JSON 内容，完整响应内容如下：")
                print(full_response)
        except Exception as e:
            print(f"调用 LLM API 时出现错误: {e}")

        retries += 1

    print("多次尝试后仍未找到有效路径。")
    return None

# 本地检查路径是否有效
def is_valid_path(path):
    for x, y in path:
        if x < 0 or x >= len(MAP[0]) or y < 0 or y >= len(MAP) or MAP[y][x] == 1:
            return False
    return True

# 辅助函数：从问题中提取终点
def get_end_point(question):
    for location in DESTINATIONS:
        if location in question:
            return DESTINATIONS[location]
    return None


# 处理指令
def handle_command():
    question = entry.get()
    start = character.get_coordinate()

    def thread_task():
        path = llm_path_planning(start, question)
        root.after(0, lambda: update_character_path(path))

    threading.Thread(target=thread_task).start()


def update_character_path(path):
    if path:
        character.path = path[1:]
        character.draw_path(path)
        character.is_moving = True
        character.follow_path()
    else:
        print("未找到有效路径。")


# 创建主窗口
root = tk.Tk()
root.title("虚拟世界")

# 创建画布
canvas = tk.Canvas(root, width=len(MAP[0]) * TILE_SIZE, height=len(MAP) * TILE_SIZE, bg="white")
canvas.pack()

# 绘制地图
for y in range(len(MAP)):
    for x in range(len(MAP[0])):
        if MAP[y][x] == 1:
            canvas.create_rectangle(
                x * TILE_SIZE, y * TILE_SIZE,
                (x + 1) * TILE_SIZE, (y + 1) * TILE_SIZE,
                fill="gray"
            )

# 绘制地点
for name, (x, y) in DESTINATIONS.items():
    canvas.create_rectangle(
        x * TILE_SIZE, y * TILE_SIZE,
        (x + 1) * TILE_SIZE, (y + 1) * TILE_SIZE,
        fill="green"
    )
    canvas.create_text(
        (x + 0.5) * TILE_SIZE, (y + 0.5) * TILE_SIZE,
        text=f"{name}\n({x}, {y})",
        fill="white"
    )

# 创建角色，设置初始坐标为 (4, 5)
character = Character(canvas, 4, 5)

# 创建文本输入框
entry = tk.Entry(root, width=70)  # 可以根据需要调整 width 的值
entry.pack()

# 按钮
button = tk.Button(root, text="提交指令", command=handle_command)
button.pack()

root.mainloop()