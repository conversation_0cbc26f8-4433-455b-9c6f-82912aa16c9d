import re

def is_local_package(line):
    # 检查是否是本地包的正则表达式，可以根据需要调整
    local_package_pattern = re.compile(r'^(?:-e )?file://|^\.\/|^\.\\|^[a-zA-Z]:\\|@ file://')
    return local_package_pattern.search(line)

with open('../../requirements.txt', 'r') as infile, open('requirements.txt', 'w') as outfile:
    for line in infile:
        if not is_local_package(line.strip()):
            outfile.write(line)

