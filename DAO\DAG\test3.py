import itertools
import math
import time
import collections

# Assuming test2.py exists and has parse_graph_edges
# from test2 import parse_graph_edges

# Mock parse_graph_edges for standalone execution if test2.py is not available
def parse_graph_edges(graph_string):
    """
    Parses a string representation of graph edges into a list of tuples.
    Example input: "[('a','c'), ('a','d'), ('b','c')]"
    """
    try:
        # Safely evaluate the string as a Python literal
        graph_list = eval(graph_string)
        if not isinstance(graph_list, list):
            raise ValueError("Input is not a list.")
        parsed_edges = []
        for edge in graph_list:
            if isinstance(edge, tuple) and len(edge) == 2:
                parsed_edges.append(tuple(str(item).strip() for item in edge))
            else:
                raise ValueError("Each element must be a tuple of two items.")
        return parsed_edges
    except Exception as e:
        print(f"Error parsing graph string: {e}")
        return []


def get_coalitions(players):
    """Generates all non-empty coalitions for a set of players."""
    #  对于给定的玩家集合，生成所有非空联盟。
    return [frozenset(c) for i in range(1, len(players) + 1) for c in itertools.combinations(players, i)]


def get_coalitions_by_size(players):
    """生成所有非空联盟并按大小分组。"""
    coalitions_by_size = {}
    for i in range(1, len(players) + 1):
        coalitions_by_size[i] = [frozenset(c) for c in itertools.combinations(players, i)]
    return coalitions_by_size


def find_connected_components(coalition, graph):
    """Finds connected components within a coalition given a graph."""
    #  给定一个图，找出联盟内的连接组件。
    players_in_coalition = set(coalition)
    unvisited = set(players_in_coalition)
    components = []

    # Convert graph edges to an adjacency list for easier neighbor lookup
    adjacency_list = collections.defaultdict(list)
    for u, v in graph:
        adjacency_list[u].append(v)
        adjacency_list[v].append(u)

    while unvisited:
        start_node = next(iter(unvisited))
        component = set()
        queue = [start_node]
        visited_in_component = set()

        while queue:
            player = queue.pop(0)
            if player not in visited_in_component:
                visited_in_component.add(player)
                component.add(player)
                unvisited.discard(player)

                # Find neighbors in the graph that are also in the coalition
                #  找到图中也是联盟成员的邻居
                neighbors = adjacency_list.get(player, [])
                coalition_neighbors = [neighbor for neighbor in neighbors if neighbor in players_in_coalition]

                for neighbor in coalition_neighbors:
                    if neighbor not in visited_in_component:
                        queue.append(neighbor)

        if component:
            components.append(frozenset(component))

    return components


def calculate_modified_game_value(original_game_values, coalition, graph):
    """Calculates the value of a coalition in the modified game (v/g)."""
    #  计算修改后的博弈 (v/g) 中联盟的值。
    connected_components = find_connected_components(coalition, graph)
    modified_value = 0
    for component in connected_components:
        # Ensure component exists as a key in the original game's characteristic function
        # 确保组件作为原始博弈的特征函数中的键存在
        if component in original_game_values:
            modified_value += original_game_values[component]
        # Handle cases where a component might not have a defined value in the input game
        # (e.g., if input game only specifies values for single players and grand coalition)
        # For this implementation, we assume values are defined for all relevant subsets
        # based on the paper's context of characteristic function games.
        #  处理输入博弈中组件可能没有定义值的情况
        #  （例如，如果输入博弈仅指定单个玩家和总联盟的值）
        #  对于此实现，我们假设基于论文的特征函数博弈的上下文，为所有相关子集定义了值。
        elif frozenset() not in original_game_values:  # Check if empty set is handled, although not typical for CFG
            pass  # Or handle error/warning if a component value is missing
        else:
            # If the empty set has a value defined, it might be relevant
            #  如果为空集定义了值，它可能是相关的
            if frozenset() in original_game_values:
                modified_value += original_game_values[frozenset()]

    return modified_value


def calculate_shapley_value(game_values, players):
    """Calculates the Shapley value for each player in a game."""
    #  计算博弈中每个玩家的 Shapley 值。
    n = len(players)
    shapley_values = {player: 0 for player in players}
    all_coalitions = get_coalitions(players)

    for player in players:
        for coalition in all_coalitions:
            if player in coalition:
                coalition_without_player = frozenset(coalition - {player})
                # Ensure coalition_without_player is a valid key or handle empty set
                # 确保 coalition_without_player 是一个有效的键或处理空集
                val_coalition = game_values.get(coalition, 0)  # Default to 0 if coalition not in game_values
                val_coalition_without = game_values.get(coalition_without_player,
                                                        0)  # Default to 0 for empty set or missing key

                marginal_contribution = val_coalition - val_coalition_without

                s = len(coalition)
                # The original Shapley formula uses (s-1)! * (n-s)! / n!
                # s is the size of the coalition *with* the player
                # n-s is the number of players outside the coalition *with* the player
                #  原始的 Shapley 公式使用 (s-1)! * (n-s)! / n!
                #  s 是包含玩家的联盟的大小
                #  n-s 是不包含玩家的联盟之外的玩家数量
                numerator = math.factorial(s - 1) * math.factorial(n - s)
                denominator = math.factorial(n)
                weight = numerator / denominator

                shapley_values[player] += weight * marginal_contribution
    return shapley_values


def calculate_traditional_shapley(original_game_values, players):
    """计算传统的Shapley值（不考虑图的连通性）"""
    n = len(players)
    shapley_values = {player: 0 for player in players}
    all_coalitions = get_coalitions(players)
    
    for player in players:
        for coalition in all_coalitions:
            if player in coalition:
                coalition_without_player = frozenset(coalition - {player})
                # 获取联盟价值
                val_coalition = original_game_values.get(coalition, 0)
                val_coalition_without = original_game_values.get(coalition_without_player, 0)
                
                # 计算边际贡献
                marginal_contribution = val_coalition - val_coalition_without
                
                # 计算权重
                s = len(coalition)
                numerator = math.factorial(s - 1) * math.factorial(n - s)
                denominator = math.factorial(n)
                weight = numerator / denominator
                
                # 更新Shapley值
                shapley_values[player] += weight * marginal_contribution
    
    return shapley_values


# 示例特征函数: ν(S) = |S|^2
def nu(S):
    """计算联盟S的特征函数值：ν(S) = |S|^2"""
    return len(S) ** 2


def dfs_myerson_value(G, characteristic_function):
    """使用DFS算法计算Myerson值"""
    # 从图中提取所有玩家
    players = set()
    for edge in G:
        players.update(edge)
    players = list(players)
    
    # 按度数降序排列节点
    adjacency_list = collections.defaultdict(list)
    for u, v in G:
        adjacency_list[u].append(v)
        adjacency_list[v].append(u)
    
    # 计算每个节点的度数
    degree = {player: len(adjacency_list[player]) for player in players}
    # 按度数降序排序
    sorted_players = sorted(players, key=lambda p: -degree[p])
    
    # 初始化Myerson值
    myerson_values = {player: 0.0 for player in players}
    
    # 对每个玩家计算Myerson值
    for vi in sorted_players:
        # 初始化DFS需要的集合
        X = set()      # 排除集 X
        XN = set()     # 排除邻居集
        S = {vi}       # 当前子图，初始只包含当前玩家
        path = [vi]    # 当前路径，初始只包含当前玩家
        
        # 开始递归DFS
        DFS_Myerson_Rec(adjacency_list, path, S, X, XN, 0, characteristic_function, myerson_values, vi)
    
    # 使用DFS找出所有连通子图
    connected_subgraphs = dfs_find_connected_subgraphs(adjacency_list, players)
    
    # 按大小分组连通子图
    subgraphs_by_size = {}
    for subgraph in connected_subgraphs:
        size = len(subgraph)
        if size not in subgraphs_by_size:
            subgraphs_by_size[size] = []
        subgraphs_by_size[size].append(subgraph)
    
    # 打印DFS找到的连通子图
    print("\nDFS遍历找到的连通子图（联盟）:")
    for size in sorted(subgraphs_by_size.keys()):
        if size > 0:  # 跳过空集
            print(f"{size}级联盟 (共{len(subgraphs_by_size[size])}个):")
            for subgraph in subgraphs_by_size[size]:
                print(f"  {subgraph}")
    
    return myerson_values

def DFS_Myerson_Rec(adjacency_list, path, S, X, XN, start_it, characteristic_function, myerson_values, vi):
    """
    DFS递归函数，用于枚举连通子图并计算Myerson值贡献
    
    参数:
    - adjacency_list: 邻接表表示的图
    - path: 当前路径
    - S: 当前子图
    - X: 已排除的节点集合
    - XN: 已排除的邻居节点集合
    - start_it: 邻居列表中的起始索引
    - characteristic_function: 特征函数，计算联盟价值
    - myerson_values: 每个玩家的Myerson值
    - vi: 当前正在计算Myerson值的玩家
    """
    # 获取当前路径的最后一个节点
    v = path[-1]
    
    # 获取节点v的邻居，并按度数降序排序
    neighbors = sorted(adjacency_list[v], key=lambda n: -len(adjacency_list[n]))
    
    # 遍历邻居
    for it in range(start_it, len(neighbors)):
        u = neighbors[it]
        
        # 如果u不在当前子图S中且不在已排除的节点集合X中
        if u not in S and u not in X:
            # 递归扩展子图
            new_path = path + [u]
            new_S = S.union({u})
            
            # 将u加入到排除的邻居集合XN中(先临时保存)
            XN.add(u)
            
            # 递归继续DFS
            DFS_Myerson_Rec(adjacency_list, new_path, new_S, X, XN, 0, characteristic_function, myerson_values, vi)
            
            # 更新已排除节点
            X.add(u)
        elif u in X and u not in XN:
            # 如果u已经在排除集合X中但不在XN中，则添加到XN
            XN.add(u)
    
    # 如果S不为空，则计算S对Myerson值的贡献
    if len(S) > 0:
        # 计算S的值
        s_value = characteristic_function(S)
        
        # 为S中的所有玩家计算贡献
        if vi in S:
            # 贡献: [(|S|-1)! |XN|! / (|S|+|XN|)!] * v(S)
            weight = (math.factorial(len(S)-1) * math.factorial(len(XN))) / math.factorial(len(S)+len(XN))
            myerson_values[vi] += weight * s_value
        
        # 为XN中的所有玩家计算贡献
        if vi in XN:
            # 贡献: -[(|S|)! (|XN|-1)! / (|S|+|XN|)!] * v(S)
            weight = (math.factorial(len(S)) * math.factorial(len(XN)-1)) / math.factorial(len(S)+len(XN))
            myerson_values[vi] -= weight * s_value
    
    # 回溯：移除路径的最后一个节点
    if len(path) > 0:
        path.pop()
    
    if len(path) > 0:
        # 获取新的最后一个节点
        last_v = path[-1]
        # 找到回溯前节点在新的最后节点邻居列表中的位置
        new_start_it = 0
        neighbors_of_last = adjacency_list[last_v]
        if v in neighbors_of_last:
            new_start_it = neighbors_of_last.index(v) + 1
        
        # 递归，从新的起始位置继续
        DFS_Myerson_Rec(adjacency_list, path, S, X, XN, new_start_it, characteristic_function, myerson_values, vi)

def compute_myerson_value(G, characteristic_function=None, custom_values=None):
    """
    计算Myerson值

    参数:
    G: 图结构，表示为边的集合
    characteristic_function: 特征函数，计算联盟价值，当custom_values为None时使用
    custom_values: 用户提供的自定义联盟值，如果提供则优先使用

    返回:
    字典，包含每个玩家的Myerson值
    """
    start_time = time.time()

    # 从图中提取所有玩家
    players = set()
    for edge in G:
        players.update(edge)
    players = list(players)

    # 生成所有可能的联盟
    all_coalitions = get_coalitions(players)

    # 按大小分组联盟并显示
    coalitions_by_size = get_coalitions_by_size(players)
    print("\n所有可能的联盟:")
    for size, coalitions in coalitions_by_size.items():
        print(f"{size}级联盟 (共{len(coalitions)}个):")
        for coalition in coalitions:
            print(f"  {coalition}")

    # 计算原始博弈值
    original_game_values = {}
    if custom_values:
        # 使用用户提供的自定义联盟值
        original_game_values = custom_values
    else:
        # 使用特征函数计算联盟值
        for coalition in all_coalitions:
            original_game_values[coalition] = characteristic_function(coalition)
    
    # 确保空集的值为0
    original_game_values[frozenset()] = 0
    
    # 计算修改后的博弈值（基于图的连通性）
    modified_game_values = {}
    modified_game_values[frozenset()] = 0  # 空集的值
    
    # 处理所有非空联盟
    for coalition in all_coalitions:
        # 找出联盟的连通分量
        components = find_connected_components(coalition, G)
        # 计算修改后的值
        value = 0
        for component in components:
            value += original_game_values.get(component, 0)
        modified_game_values[coalition] = value
        
    # 检测是否为图片中的特殊示例
    is_special_example = False
    if len(players) == 3 and set(players) == {'1', '2', '3'} or set(players) == {'a', 'b', 'c'}:
        # 检查是否是特定图结构: [('1','2'), ('1','3')]或[('a','b'), ('a','c')]
        possible_edges = [('1', '2'), ('1', '3'), ('a', 'b'), ('a', 'c'), 
                          ('2', '1'), ('3', '1'), ('b', 'a'), ('c', 'a')]
        if len(G) == 2 and all(edge in possible_edges for edge in G):
            # 这可能是图片中的示例，但我们还是用算法计算
            print("\n检测到可能是图片中的特殊示例游戏，将使用算法计算Myerson值...")
            is_special_example = False

    # 创建一个函数，用于在计算Myerson值时查询联盟的值
    def get_coalition_value(coalition):
        return original_game_values.get(frozenset(coalition), 0)

    # 计算传统的Shapley值（不考虑图结构）
    traditional_shapley_values = calculate_traditional_shapley(original_game_values, players)
    
    # 使用DFS算法计算Myerson值
    print("\n使用DFS算法计算Myerson值...")
    myerson_values = dfs_myerson_value(G, get_coalition_value)
    
    # 计算大联盟价值和Shapley值总和
    grand_coalition = frozenset(players)
    grand_coalition_value = original_game_values.get(grand_coalition, 0)
    shapley_total = sum(traditional_shapley_values.values())
    myerson_total = sum(myerson_values.values())
    
    # 计算基于图结构的Shapley值（Myerson值的另一种计算方式）
    print("\n计算基于图结构的Shapley值（另一种Myerson值计算方式）...")
    myerson_values_alt = calculate_shapley_value(modified_game_values, players)
    myerson_alt_total = sum(myerson_values_alt.values())
    
    print(f"\n玩家: {players}")
    print(f"图结构: {G}")
    print(f"大联盟价值: {grand_coalition_value}")
    print(f"传统Shapley值总和: {shapley_total:.4f}")
    print(f"DFS计算的Myerson值总和: {myerson_total:.4f}")
    print(f"另一种计算的Myerson值总和: {myerson_alt_total:.4f}")
    
    print("\nMyerson值 (基于DFS算法):")
    for player, value in myerson_values.items():
        # 添加除零检查
        if myerson_total != 0:
            percentage = (value / myerson_total) * 100
            print(f"玩家 {player}: {value:.4f} ({percentage:.2f}%)")
        else:
            print(f"玩家 {player}: {value:.4f} (无法计算百分比，总和为零)")
            
    print("\nMyerson值 (基于修改后博弈的Shapley值):")
    for player, value in myerson_values_alt.items():
        # 添加除零检查
        if myerson_alt_total != 0:
            percentage = (value / myerson_alt_total) * 100
            print(f"玩家 {player}: {value:.4f} ({percentage:.2f}%)")
        else:
            print(f"玩家 {player}: {value:.4f} (无法计算百分比，总和为零)")
    
    print("\n传统的Shapley值(不考虑图结构):")
    for player, value in traditional_shapley_values.items():
        # 添加除零检查
        if shapley_total != 0:
            percentage = (value / shapley_total) * 100
            print(f"玩家 {player}: {value:.4f} ({percentage:.2f}%)")
        else:
            print(f"玩家 {player}: {value:.4f} (无法计算百分比，总和为零)")

    end_time = time.time()
    print(f"\n计算Myerson值所需时间: {end_time - start_time:.4f} 秒")
    
    # 返回DFS计算的Myerson值，与test2.py的结果保持一致
    return myerson_values


def get_connected_coalitions(players, graph):
    """
    根据图结构找出所有连通的联盟

    参数:
    - players: 所有玩家列表
    - graph: 图结构，表示为边的集合，如 [('a','b'), ('b','c'), ...]

    返回:
    - 所有连通联盟的列表
    """
    all_coalitions = get_coalitions(players)
    connected_coalitions = []

    for coalition in all_coalitions:
        # 对每个联盟，检查它是否是连通的
        components = find_connected_components(coalition, graph)
        # 如果联盟只有一个连通分量，并且该分量包含了联盟中的所有玩家，则该联盟是连通的
        if len(components) == 1 and len(components[0]) == len(coalition):
            connected_coalitions.append(coalition)

    # 添加空集
    connected_coalitions.append(frozenset())

    return connected_coalitions


def dfs_find_connected_subgraphs(adjacency_list, players):
    """
    使用DFS算法找出图中所有连通的子图（联盟）
    
    参数:
    adjacency_list: 邻接表表示的图
    players: 所有玩家列表
    
    返回:
    连通子图（联盟）的列表
    """
    connected_subgraphs = []
    visited_subgraphs = set()  # 用于跟踪已经找到的子图
    
    # 对每个玩家作为起点进行DFS
    for start_player in players:
        # 从单个玩家开始
        stack = [(start_player, frozenset([start_player]))]  # (当前节点, 当前子图)
        
        while stack:
            current, subgraph = stack.pop()
            
            # 如果这个子图已经被访问过，跳过
            if subgraph in visited_subgraphs:
                continue
            
            # 记录这个连通子图
            if subgraph not in connected_subgraphs:
                connected_subgraphs.append(subgraph)
                visited_subgraphs.add(subgraph)
            
            # 遍历当前节点的所有邻居
            for neighbor in adjacency_list.get(current, []):
                # 创建包含邻居的新子图
                new_subgraph = frozenset(subgraph.union([neighbor]))
                
                # 如果这个新子图没有被访问过，加入栈中
                if new_subgraph not in visited_subgraphs:
                    stack.append((neighbor, new_subgraph))
    
    # 添加空集
    connected_subgraphs.append(frozenset())
    
    return connected_subgraphs


def calculate_optimized_fair_allocation(players, original_game_values, graph):
    """
    优化的公平分配规则计算 - 考虑图结构处理联盟
    
    这个函数计算基于图结构的联盟值，对于非连通联盟，根据连通分量计算值。
    
    参数:
    - players: 玩家列表
    - original_game_values: 原始博弈中所有联盟的价值，以 frozenset 为键
    - graph: 图结构，表示为边的集合，如 [('a','b'), ('b','c'), ...]
    
    返回:
    - 修改后博弈的Shapley值，即公平分配值
    """
    # 1. 找出连通联盟
    connected_coalitions = get_connected_coalitions(players, graph)
    all_coalitions = get_coalitions(players)
    print(f"需要计算的连通联盟数量: {len(connected_coalitions)}，而不是全部 {2**len(players)} 个联盟")
    
    # 2. 为所有联盟计算修改后博弈值
    modified_game_values = {}
    
    # 先处理空集
    modified_game_values[frozenset()] = 0
    
    # 处理所有非空联盟
    for coalition in all_coalitions:
        if len(coalition) == 1:  # 单一玩家联盟
            modified_game_values[coalition] = original_game_values.get(coalition, 0)
        else:
            # 判断联盟是否连通
            components = find_connected_components(coalition, graph)
            if len(components) == 1:  # 连通联盟
                modified_game_values[coalition] = original_game_values.get(coalition, 0)
            else:  # 非连通联盟
                # 对于非连通联盟，累加其连通分量的值
                value = 0
                for component in components:
                    value += original_game_values.get(component, 0)
                modified_game_values[coalition] = value
    
    # 3. 计算修改后博弈的Shapley值
    fair_allocation = calculate_shapley_value(modified_game_values, players)
    
    return fair_allocation


def parse_coalition_value(coalition_str, value):
    """将字符串格式的联盟转换为frozenset"""
    # 将例如 ('a','b','c') 解析为 frozenset({'a', 'b', 'c'})
    # 去除括号和空格，然后分割
    if coalition_str == '()':
        return frozenset(), value
    coalition_str = coalition_str.strip("()").replace("'", "").replace('"', '')
    players = [p.strip() for p in coalition_str.split(',')]
    return frozenset(players), value


def parse_graph_edges(edges_str):
    """解析图的边缘结构"""
    try:
        # 安全地评估字符串作为Python字面量
        graph_list = eval(edges_str)
        if not isinstance(graph_list, list):
            raise ValueError("输入不是列表。")
        
        parsed_edges = []
        for edge in graph_list:
            if isinstance(edge, tuple) and len(edge) == 2:
                parsed_edges.append(tuple(str(item).strip() for item in edge))
            else:
                raise ValueError("每个元素必须是两项的元组。")
        
        return parsed_edges
    except Exception as e:
        print(f"解析图字符串时出错: {e}")
        return []


def parse_coalition_values(values_str):
    """解析联盟值字典"""
    # 去除最外层的花括号
    values_str = values_str.strip("{}")
    if not values_str:
        return {}
    
    result = {}
    # 找到所有键值对
    entries = []
    start = 0
    level = 0
    i = 0
    
    while i < len(values_str):
        char = values_str[i]
        # 处理键部分
        if char == '(' and (i == 0 or values_str[i-1] != "'"):
            if level == 0:
                start = i
            level += 1
        elif char == ')' and (i == len(values_str)-1 or values_str[i+1] != "'"):
            level -= 1
            if level == 0:
                # 寻找冒号
                j = i + 1
                while j < len(values_str) and values_str[j] != ':':
                    j += 1
                
                if j < len(values_str):
                    # 寻找值的结束位置
                    k = j + 1
                    while k < len(values_str) and values_str[k] not in [',']:
                        k += 1
                    
                    key_str = values_str[start:i+1]
                    value_str = values_str[j+1:k].strip()
                    
                    try:
                        value = int(value_str)
                        coalition, _ = parse_coalition_value(key_str, 0)
                        result[coalition] = value
                    except ValueError:
                        # 处理值不是整数的情况
                        pass
                    
                    i = k
                    if i < len(values_str) and values_str[i] == ',':
                        i += 1
                    continue
        
        i += 1
    
    # 添加空集的值
    result[frozenset()] = 0
    
    return result


def main():
    try:
        # 用户输入玩家列表
        players_input = input("请输入玩家列表（用逗号分隔，例如 a,b,c,d,e,f）: ")
        players = [p.strip() for p in players_input.split(',')]

        # 用户输入图的结构
        print("请输入图的结构（例如 [('a','c'), ('a','d'), ('b','c')]），或直接回车使用默认图结构: ")
        graph_input = input()
        if not graph_input:
            # 使用默认图结构的例子
            graph = [('a', 'c'), ('a', 'd'), ('a', 'e'), ('b', 'c'), ('b', 'd'), ('b', 'e'), 
                    ('c', 'd'), ('c', 'e'), ('d', 'e'), ('c', 'f'), ('d', 'f'), ('e', 'f')]
        else:
            graph = parse_graph_edges(graph_input)
        
        # 检查是否是图片中的特殊示例
        is_special_example = False
        if len(players) == 3 and set(players) == {'1', '2', '3'} or set(players) == {'a', 'b', 'c'}:
            # 检查是否是特定图结构: [('1','2'), ('1','3')]或[('a','b'), ('a','c')]
            possible_edges = [('1', '2'), ('1', '3'), ('a', 'b'), ('a', 'c'), 
                             ('2', '1'), ('3', '1'), ('b', 'a'), ('c', 'a')]
            if len(graph) == 2 and all(edge in possible_edges for edge in graph):
                print("\n检测到可能是图片中的特殊示例游戏！")
                use_special = input("是否要使用图片中的特殊值？(y/n，默认n): ").strip().lower()
                if use_special == 'y':
                    is_special_example = True
                    # 为特殊示例设置特定的联盟值
                    original_game_values = {
                        frozenset({'a'}): 0, frozenset({'b'}): 0, frozenset({'c'}): 0,
                        frozenset({'a', 'c'}): 6, frozenset({'a', 'b'}): 12, frozenset({'b', 'c'}): 6,
                        frozenset({'a', 'b', 'c'}): 12, frozenset(): 0
                    }
                    # 如果玩家是1,2,3而不是a,b,c，进行转换
                    if set(players) == {'1', '2', '3'}:
                        translation = {'a': '1', 'b': '2', 'c': '3'}
                        translated_values = {}
                        for coalition, value in original_game_values.items():
                            new_coalition = frozenset(translation.get(p, p) for p in coalition)
                            translated_values[new_coalition] = value
                        original_game_values = translated_values
                    
                    # 直接计算Myerson值，跳过输入联盟值
                    myerson_values = compute_myerson_value(graph, None, original_game_values)
                    return
        
        # 用户输入联盟值
        print("请输入联盟值（以字典格式，例如 {('a',): 5, ('a','b'): 15}），或直接回车使用默认联盟值: ")
        values_input = input()
        
        # 定义联盟值字典
        original_game_values = {}
        
        if not values_input:
            # 使用默认联盟值的例子 - 这里使用特征函数计算
            for i in range(1, len(players) + 1):
                for coalition in itertools.combinations(players, i):
                    original_game_values[frozenset(coalition)] = nu(frozenset(coalition))
            original_game_values[frozenset()] = 0
        else:
            try:
                # 解析用户输入的联盟值
                input_values = eval(values_input)
                if isinstance(input_values, dict):
                    for key, value in input_values.items():
                        if isinstance(key, tuple):
                            original_game_values[frozenset(key)] = value
                        else:
                            # 处理输入不是元组的情况
                            print(f"警告: 跳过无效的联盟键 {key}")
                    
                    # 确保空集有值
                    original_game_values[frozenset()] = 0
                else:
                    print("警告: 输入的联盟值格式不正确，将使用默认联盟值")
                    # 使用默认联盟值
                    for i in range(1, len(players) + 1):
                        for coalition in itertools.combinations(players, i):
                            original_game_values[frozenset(coalition)] = nu(frozenset(coalition))
                    original_game_values[frozenset()] = 0
            except Exception as e:
                print(f"解析联盟值时出错: {e}，将使用默认联盟值")
                # 使用默认联盟值
                for i in range(1, len(players) + 1):
                    for coalition in itertools.combinations(players, i):
                        original_game_values[frozenset(coalition)] = nu(frozenset(coalition))
                original_game_values[frozenset()] = 0
        
        if graph:  # 只在图解析成功时继续
            # 计算Myerson值，传入自定义联盟值
            myerson_values = compute_myerson_value(graph, nu, original_game_values)
        else:
            print("无法解析输入的图结构。")
    except EOFError:
        print("\n输入被中断，程序退出。")
    except Exception as e:
        print(f"\n发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
