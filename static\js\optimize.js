// 全局变量声明
var popup = document.getElementById("popupWindow");
var closeButton = document.querySelector(".close");
var titleBar = document.querySelector(".popup-header");
var inputText = document.getElementById("inputText");
var displayTextElement = document.getElementById("displayText"); // 全局定义
var isDragging = false;
var shiftX, shiftY;
var tempSelectedText = "";
var tempSelectedHtml = ""; // 存储选中的HTML内容
var tempSelectedRange = null; // 存储选中的Range对象
var selection = window.getSelection();
var editor = document.getElementById("editor");
var placeholderSpan = document.getElementById("placeholder")
var history_options_num = 0;

editor.onclick = function (event) {
  var placeholderSpan = document.getElementById("placeholder");
  if (placeholderSpan && event.target === editor) {
    placeholderSpan.parentNode.removeChild(placeholderSpan);
  }
};


function copyEditorContent(button) {
  var editorContent = document.getElementById("editor").innerText;
  var tempTextArea = document.createElement("textarea");
  tempTextArea.value = editorContent;
  document.body.appendChild(tempTextArea);
  tempTextArea.select();
  document.execCommand("copy");
  document.body.removeChild(tempTextArea);
  var originalContent = button.innerHTML;
  button.innerHTML =
    '<img src="static/pic/ok.png" alt="Checkmark Icon" style="height: 16px; vertical-align: middle; margin-right: 5px;">复制成功';
  setTimeout(function () {
    button.innerHTML = originalContent;
  }, 500);
}


titleBar.addEventListener("mousedown", function (event) {
  event.preventDefault();
  shiftX = event.clientX - popup.offsetLeft;
  shiftY = event.clientY - popup.offsetTop;
  isDragging = true;
});

document.addEventListener("mousemove", function (event) {
  if (isDragging) {
    popup.style.left = event.clientX - shiftX + "px";
    popup.style.top = event.clientY - shiftY + "px";
  }
});

document.addEventListener("mouseup", function () {
  isDragging = false;
});


document.addEventListener("mouseup", debounce(handleMouseUp, 200));

function handleMouseUp() {
  selection = window.getSelection();
  const editButton = document.getElementById("editIcon");
  const editor = document.getElementById("editor");

  if (!editButton || !editor) return;

  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0);
    const isSelectionInsideEditor = editor.contains(range.commonAncestorContainer);

    if (selection.toString().trim() !== "" && isSelectionInsideEditor) {
      showEditButton(range, editButton, editor);
      saveSelectionData(selection, range);
    } else {
      hideEditButton(editButton);
    }
  } else {
    hideEditButton(editButton);
  }
}

function showEditButton(range, editButton, editor) {
  const rect = range.getBoundingClientRect();
  const editorRect = editor.getBoundingClientRect();
  editButton.style.left = `${rect.left - editorRect.left}px`;
  editButton.style.top = `${rect.bottom - editorRect.top + 30}px`;
  editButton.style.position = "absolute";
  editButton.style.display = "block";

}

function hideEditButton(editButton) {
  editButton.style.display = "none";
}

function saveSelectionData(selection, range) {
  window.tempSelectedText = selection.toString();
  window.tempSelectedHtml = range.cloneContents();
  window.tempSelectedRange = range;
}

function debounce(func, wait) {
  let timeout;
  return function(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}







titleBar.addEventListener("mousedown", function (event) {
  event.preventDefault();
  shiftX = event.clientX - popup.offsetLeft;
  shiftY = event.clientY - popup.offsetTop;
  isDragging = true;
});


closeButton.addEventListener("click", function () {
  resetPopup()
  popup.style.display = "none";
  tempSelectedText = "";
  tempSelectedHtml = "";
  tempSelectedRange = null; // 重置选中的Range对象
});

document.getElementById("editIcon").addEventListener("click", function () {
  popup.style.display = "block";
  document.getElementById("selectText").innerText= selection.toString()
});

//提交按钮
document.getElementById("submit").addEventListener("click", function () {
  var inputTextValue = inputText.value.trim();
  var answerText = displayTextElement.value.trim();
  var editorText = document.getElementById("editor").textContent.trim();
  var formattedText = ""

  if (editorText.length > 0 && inputTextValue.length > 0) {
    if (answerText.length > 0){
      formattedText = answerText + " \n修改要求: " + inputTextValue;
    }else{
      formattedText = tempSelectedText + " \n修改要求: " + inputTextValue;
    }
    console.log(formattedText)
    var source = 3;
    messageBuffer = "";
    var model = "";
    if (socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify({formattedText,source,model}));
      $('#loadingIndicator3').css("display","inline-block")
    } else {
      // 可以在这里通知用户WebSocket未连接
      $('#loginModal').modal('show')
      console.error("连接失败，请刷新页面重试");
    }

  }
});



function resetPopup() {
  inputText.value = "";
  displayTextElement.value = "";
}

document.getElementById("insert").addEventListener("click", function () {
  if (
    tempSelectedText &&
    displayTextElement.innerText !== "这里会显示文本..."
  ) {
    var responseHtml = displayTextElement.value; // 获取模型响应的HTML内容

    if (tempSelectedRange) {
      tempSelectedRange.deleteContents(); // 删除选中的内容
      var newNode = document.createElement("div");
      newNode.innerHTML = responseHtml;
      var frag = document.createDocumentFragment(),
        child;
      while ((child = newNode.firstChild)) {
        frag.appendChild(child);
      }
      tempSelectedRange.insertNode(frag); // 插入新的HTML内容0
    }

  }

});



  var cleanButton = document.getElementById("CleanText");
  cleanButton.addEventListener("click", function () {
    editor.innerHTML = "";
    editor.appendChild(placeholderSpan);
    placeholderSpan.style.display = "inline";
  });

  editor.addEventListener("input", function () {
    if (editor.textContent.trim() !== "") {
      placeholderSpan.style.display = "none";
    } else {
      placeholderSpan.style.display = "inline";
    }
  });

socket.onmessage = function(event) {
      const data = JSON.parse(event.data);
      if (data["target"] === 3) {
        let displayText = document.getElementById('displayText');
        if ("message" in data) {
          messageBuffer += data.message;
          messageBuffer = messageBuffer.replace(/<br>/g, '\n');
          if (displayText) {
            displayText.value = messageBuffer;
          }
        }
        if ("status" in data) {
          if (data.status === 1) {
            console.log("完毕")

            let select = document.getElementById("history");
            history_options_num += 1
            // 如果列表长度超过10，移除列表中的第一个值
            if (history_options_num > 10) {
              select.remove(0)
              history_options_num -= 1
            }

            let opt = document.createElement("option");
            opt.text = messageBuffer
            select.appendChild(opt);


          } else if (data.status === 0) {
            console.log("服务器网络故障")
          }
          $('#loadingIndicator3').css("display","none")
        }
      }
}
document.getElementById("history").addEventListener("change", function() {
        let inputField = document.getElementById("displayText");
        let selectedOption = this.options[this.selectedIndex].text;
        inputField.value = selectedOption;
    });



