# consumers.py
import sys

from Multimodal_gemini import AsyncImageAnalyzer

# 打开文件，将标准输出重定向到文件
sys.stdout = open('output_logP.txt', 'a', encoding='utf-8', buffering=1)  # 使用 'a' 模式来追加数据

import json
import logging
import os
import base64
from datetime import datetime
from openai import AsyncOpenAI
from style_content.tools.update_usage import aupdate_usage
from style_content.tools.count_token import num_tokens_from_messages
from channels.generic.websocket import AsyncWebsocketConsumer
from style_content.tools.update_content import aupdate_content
from style_content.tools.load_config import Config
import asyncio

config = Config()
openai_transfer_api_key = config.get_config()["openai_transfer_apikey"]
openai_transfer_api_key2 = config.get_config()["openai_transfer_apikey2"]
openai_api_key = config.get_config()["openai_apikey"]
deepseek_api_key = config.get_config()["deepseek_apikey"]
ft_model_name = config.get_config()["ft_model_name"]
en_prompt = config.get_config()["en_prompt"]
en_prompt_withoutexamples = config.get_config()["en_prompt_withoutexamples"]
OA_prompt = config.get_config()["OA_prompt"]
WC_prompt = config.get_config()["WC_prompt"]
DY_prompt = config.get_config()["DY_prompt"]
Red_prompt = config.get_config()["common_Red_prompt"]

# 小红书
CHAUMET_Red_prompt = config.get_config()["CHAUMET_Red_prompt"]
LV_Red_prompt = config.get_config()["LV_Red_prompt"]
Wilson_Red_prompt = config.get_config()["Wilson_Red_prompt"]
# 产品
product_message_prompt = config.get_config()["Product_message_prompt"]
product_message_OA_prompt = config.get_config()["Product_message_OA_prompt"]
# 额外要要求
extra_copy_requirements_prompt = config.get_config()["extra_copy_requirements_prompt"]
extra_example_prompt = config.get_config()["extra_example_prompt"]


#图片

outline_prompt = config.get_config()["outline_prompt"]
optimize_prompt = config.get_config()["optimize_prompt"]
Feedback_prompt = config.get_config()["Feedback_prompt"]
FinalFeedback_prompt = config.get_config()["FinalFeedback_prompt"]
openai_base_url = config.get_config()["base_url"]
openai_transfer_client = AsyncOpenAI(
    api_key=openai_transfer_api_key,
    base_url=openai_base_url,
)
openai_transfer_client2 = AsyncOpenAI(
    api_key=openai_transfer_api_key2,
    base_url=openai_base_url,
)
deepseek_client = AsyncOpenAI(
    api_key=deepseek_api_key,
    base_url="https://api.deepseek.com/"
)
ft_client = AsyncOpenAI(
    api_key=openai_api_key,
)

logger = logging.getLogger(__name__)
# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('output_logP.txt', encoding='utf-8'),
        logging.StreamHandler()  # 这会输出到控制台
    ]
)

analyzer = AsyncImageAnalyzer(
    env_path=r'D:\硕士\aiagent\gpt_CWSC\.env',
    model_name="gemini-2.0-flash",
    temperature=0,
    timeout=30
)


class Myconsumers(AsyncWebsocketConsumer):
    async def connect(self):
        if self.scope["user"].is_authenticated:
            await self.accept()
        else:
            await self.close()

    async def disconnect(self, close_code):
        print(f"Connection closed with code {close_code}")

    async def send_message(self, message, target, status):
        await self.send(text_data=json.dumps({
            'message': message,
            'target': target,
            'status': status,
        }))

    async def process_images(self, images):
        """处理接收到的图片数据并保存到指定目录"""
        try:
            # 确保图片保存目录存在
            pictures_dir = r"D:\硕士\aiagent\gpt_CWSC\pictures"
            if not os.path.exists(pictures_dir):
                os.makedirs(pictures_dir, exist_ok=True)
                logger.info(f"创建图片目录: {pictures_dir}")

            proceeded_images = []
            
            for i, image_data in enumerate(images):

                    # 获取图片信息
                image_id = image_data.get('id', f'img_{i}')
                image_name = image_data.get('name', f'image_{i}.png')
                image_base64 = image_data.get('dataUrl', '')
                proceeded_images.append((image_base64))


                    
                logger.info(f"处理图片: {image_id}, 名称: {image_name}")
            return proceeded_images
                    
                    # # 解析base64数据
                    # if image_base64.startswith('data:image/'):
                    #     # 移除data URL前缀
                    #     header, data = image_base64.split(',', 1)
                    #     # 获取图片格式
                    #     format_info = header.split(';')[0].split('/')[-1]
                    #
                    #
                    #     # 解码base64数据




        except Exception as e:
            logger.error(f"处理图片时发生错误: {str(e)}")


    async def receive(self, text_data=None, bytes_data=None):
        input_data = json.loads(text_data)
        logger.info(f"Received input_data: {input_data}")

        
        # 处理明星生日需求上传
        if input_data.get('type') == 'demands_for_stars_birthday':
            try:
                uploaded_files = input_data.get('files', [])
                logger.info(f"收到明星生日需求上传请求，文件数量: {len(uploaded_files)}")
                logger.info(f"资源{input_data['source']}")

                # 这里可以添加文件处理逻辑
                # 示例：保存文件到服务器

                # 发送处理成功的响应
                await self.send(text_data=json.dumps({
                    'type': 'demands_for_stars_birthday_response',
                    'success': True,
                    'message': '文件上传成功！系统正在处理您的数据。'
                }))
                return
            except Exception as e:
                logger.error(f"处理明星生日需求文件时发生错误: {str(e)}")
                await self.send(text_data=json.dumps({
                    'type': 'demands_for_stars_birthday_response',
                    'success': False,
                    'message': f'处理文件时出错: {str(e)}'
                }))
                return
        #
        # 继续处理其他类型的消息
        model = input_data.get('model', '')
        prompt = input_data.get('imageAnalysisRequest', '提取图片信息')
        # # logger.info(f"Received model: {input_data['source']}")
        # # logger.info(f"Received model: {model}")
        # # logger.info("----------------------------------------------")
        # # logger.info(f"Received text_data length: {len(text_data) if text_data else 0}")
        # # logger.info("----------------------------------------------")
        # # logger.info(f"Received input_data: {input_data}")
        #
        # 处理图片数据
        uploaded_images = input_data.get('uploadedImages', [])
        logger.info(f"Received {len(uploaded_images)} uploaded images")
        #
        # if uploaded_images:
        #     proceeded_images = await self.process_images(uploaded_images)
        #     result5 = await analyzer.analyze_base64_images(proceeded_images, prompt)
        #     if result5["success"]:
        #         logger.info(f"分析结果: {result5['result']}")
        #         logger.info(f"处理图片数: {result5['processed_images_count']}/{result5['total_images_count']}")
        #     else:
        #         logger.info("分析失败:", result5["error"])
        #
        #
        #
        # # 处理上传的文档
        # uploaded_documents = input_data.get('uploadedDocuments', [])
        # logger.info(f"收到 {len(uploaded_documents)} 个上传的文档")
        #
        # if uploaded_documents:
        #     import base64
        #     import os
        #     documents_dir = r"D:\硕士\aiagent\gpt_CWSC\documents"
        #     if not os.path.exists(documents_dir):
        #         os.makedirs(documents_dir, exist_ok=True)
        #         logger.info(f"创建文档目录: {documents_dir}")
        #
        #     for doc_data in uploaded_documents:
        #         doc_name = doc_data.get('name')
        #         doc_base64 = doc_data.get('content')
        #         if doc_name and doc_base64:
        #             try:
        #                 # 解析base64数据URL
        #                 header, data = doc_base64.split(',', 1)
        #                 doc_bytes = base64.b64decode(data)
        #
        #                 doc_path = os.path.join(documents_dir, doc_name)
        #                 with open(doc_path, 'wb') as f:
        #                     f.write(doc_bytes)
        #                 logger.info(f"文档已保存到: {doc_path}")
        #
        #             except Exception as e:
        #                 logger.error(f"处理文档时发生错误 {doc_name}: {str(e)}")

        # if model == "fine-tuned_model":
        #     model = ft_model_name
        # if input_data['source'] == 1:
        #     await self.get_stylecontent(input_data, model)
        # elif input_data['source'] == 2:
        #     await self.get_outline(input_data, model)
        # elif input_data['source'] == 3:#
        #     await self.optimize(input_data)
        # elif input_data['source'] == 4:#聊天功能
        #     await self.chat(input_data)
        # elif input_data['source'] == 5:  # 文案检查
        #     await self.get_checkCopywriting(input_data)
        # elif input_data['source'] == 6:  # 文案平台转换
        #     await self.platform_conversion(input_data, model)
        # elif input_data['source'] == 7:  # 文字与图片关联查询
        #     await self.wordtoimages_query(input_data, model)

    async def get_stylecontent(self, input_data, model):
        try:
            # 获取文案平台撰写要求的状态和内容
            use_content_description = input_data.get("useContentDescription", False)
            content_description_text = input_data.get("contentDescriptionText", "") if use_content_description else ""

            # 正确获取额外的文案创作要求
            extra_copy_requirements = input_data.get("contentDescriptionText", "").strip()

            # 获取是否使用示例
            ifuseexamples = input_data.get("ifuseexamples", 0)

            # extra_copy_requirements = input_data.get("contentDescriptionText", "").strip()
            # extra_example = input_data.get("ifuseexamples", 0)
            # 根据 ifuseexamples 构建 extra_example
            extra_example = ""
            if ifuseexamples == 1:
                temp = ""
                for i in range(3):
                    example = input_data.get(f"example{i + 1}", "").strip()
                    if example:
                        temp += f"\n**Example Copy {i + 1}: {example}"
                temp += "\n"
                extra_example = temp

            # 构建额外的文案创作要求部分
            extra_copy_requirements_section = ""

            if extra_copy_requirements:
                # 格式化额外的文案创作要求，包含固定文本
                extra_copy_requirements_section += extra_copy_requirements_prompt.format(
                    extra_copy_requirements=extra_copy_requirements
                )
            if extra_example:
                # 格式化额外的示例，包含固定文本
                extra_copy_requirements_section += extra_example_prompt.format(
                    extra_example=extra_example
                )
            # 初始化对话历史
            conversation_history = []
            platform = input_data.get("platform", "")
            brand = input_data.get("brand", "")

            system_content = ""  # 给 system_content 赋默认值
            content = ""

            if input_data["brand"] == "DLOCCOLD":
                if input_data["ifuseexamples"] == 0:
                    content = en_prompt.format(
                        input_data["productIntroduction"], input_data["copytype"],
                        input_data["copytheme"], content_description_text
                    )
                else:
                    content = en_prompt_withoutexamples.format(
                        extra_example, input_data["productIntroduction"], input_data["copytype"],
                        input_data["copytheme"]
                    )
                # 添加初始消息到对话历史
                conversation_history.append({"role": "system", "content": "You are a helpful assistant."})
                conversation_history.append({"role": "user", "content": content})

            elif platform == "小红书":
                if brand == "CHAUMET":
                    system_content = CHAUMET_Red_prompt

                elif brand == "LV":
                    system_content = LV_Red_prompt

                elif brand == "Wilson":
                    system_content = Wilson_Red_prompt

                else:
                    system_content = Red_prompt

                user_content = product_message_prompt.format(product_name=input_data["productName"],
                                                             product_introduction=input_data[
                                                                 "productIntroduction"],
                                                             product_audience=input_data["productAudience"],
                                                             product_selling_point=input_data[
                                                                 "productSellingPoints"],
                                                             copy_theme=input_data["copytheme2"],
                                                             extra_copy_requirements=extra_copy_requirements_section)

                # 添加品牌特定内容到对话历史
                conversation_history.append({"role": "system", "content": system_content})
                conversation_history.append({"role": "user", "content": user_content})

            else:
                if platform in ["微博", "视频号"]:
                    system_content = WC_prompt
                    user_content = product_message_prompt.format(product_name=input_data["productName"],
                                                                 product_introduction=input_data[
                                                                     "productIntroduction"],
                                                                 product_audience=input_data["productAudience"],
                                                                 product_selling_point=input_data[
                                                                     "productSellingPoints"],
                                                                 copy_theme=input_data["copytheme2"],
                                                                 extra_copy_requirements=extra_copy_requirements_section)
                    conversation_history.append({"role": "system", "content": system_content})
                    conversation_history.append({"role": "user", "content": user_content})

                elif platform in ["快手", "抖音"]:

                    system_content = DY_prompt
                    user_content = product_message_prompt.format(product_name=input_data["productName"],
                                                                 product_introduction=input_data[
                                                                     "productIntroduction"],
                                                                 product_audience=input_data["productAudience"],
                                                                 product_selling_point=input_data[
                                                                     "productSellingPoints"],
                                                                 copy_theme=input_data["copytheme2"],
                                                                 extra_copy_requirements=extra_copy_requirements_section)
                    conversation_history.append({"role": "system", "content": system_content})
                    conversation_history.append({"role": "user", "content": user_content})
                elif platform == "公众号":

                    system_content = OA_prompt.format(extra_copy_requirements=content_description_text)
                    user_content = product_message_OA_prompt.format(product_name=input_data["productName"],
                                                                    product_introduction=input_data[
                                                                        "productIntroduction"],
                                                                    product_audience=input_data["productAudience"],
                                                                    product_selling_point=input_data.get(
                                                                        'productSellingPoints', ''),
                                                                    copy_theme=input_data["copytheme2"],
                                                                    outline=input_data.get("outline", ""))
                    conversation_history.append({"role": "system", "content": system_content})
                    conversation_history.append({"role": "user", "content": user_content})

            print(f"整合的content:  {content if platform == 'DLOCCOLD' else system_content}")
            # print(f"整合的content:  {system_content}")
            print("----------------------------------------------")
            print(f"message: {conversation_history}")
            print("----------------------------------------------")

            # 定义模型
            initial_model = model  # 第一次调用使用 fine-tuned_model
            feedback_model = "gpt-4o"  # 第二次调用使用 o1-mini

            # 通过反馈循环生成最终内容
            final_result, initial_resp, feedback_resp, final_resp = await self.generate_with_feedback(
                initial_model=initial_model,
                feedback_model=feedback_model,
                prompt=content if platform == 'DLOCCOLD' else system_content,
                conversation_history=conversation_history,
                input_data=input_data
            )

            # 将最终结果发送到前端
            temp = final_result.replace("\n", "<br>")
            target = 1  # 对应 get_stylecontent 的 target
            await self.send(json.dumps({"message": temp, "target": target}))
            await self.send(json.dumps({"status": 1, "target": target}))
            await self.send(json.dumps({"status": 1, "target": 1}))

            # 计算 Token 使用量
            try:
                prompt_token = num_tokens_from_messages(conversation_history, initial_model)
                feedback_prompt_tokens = num_tokens_from_messages([
                    {"role": "system", "content": "You are a helpful assistant providing feedback."},
                    {"role": "user",
                     "content": Feedback_prompt.format(initial_resp.choices[0].message.content.strip(),
                                                       )}
                ], feedback_model)
                output_token = num_tokens_from_messages([{"role": "system", "content": final_result}],
                                                        initial_model) - 7
                total_prompt_tokens = prompt_token + feedback_prompt_tokens
                await aupdate_usage(self.scope["user"], initial_model, total_prompt_tokens, output_token)
            except Exception as e:
                logger.error('style_content Update_usage error: %s', str(e))

            # 更新内容
            try:
                await aupdate_content(
                    self.scope["user"], input_data.get('productIntroduction', ''), input_data.get('outline', ''),
                    final_result
                )
            except Exception as e:
                logger.error('style_content Update_content error: %s', str(e))

        except Exception as e:
            logger.error('style_content consumer error: %s', str(e))
            await self.send(json.dumps({"status": 0, "target": 1}))

    # 生成文案调用的反馈
    async def generate_with_feedback(self, initial_model, feedback_model, prompt, conversation_history, input_data,
                                     ):
        try:
            print(
                f"初始化模型： {initial_model}, 反馈模型: {feedback_model}")
            print("--------------------------------------------------------")
            print(f"Initial input data: {input_data}")
            print("--------------------------------------------------------")
            print(f"initial_conversation_history: {conversation_history}")
            # 步骤1：初始模型调用
            if initial_model.startswith("gpt"):
                initial_response = await openai_transfer_client.chat.completions.create(
                    model=initial_model,
                    messages=conversation_history,
                )
            elif initial_model.startswith("deepseek"):
                initial_response = await deepseek_client.chat.completions.create(
                    model=initial_model,
                    messages=conversation_history,
                    temperature=1.25,
                )
            elif initial_model.startswith("fine") or initial_model.startswith("ft:"):
                initial_response = await ft_client.chat.completions.create(
                    model=initial_model,
                    messages=conversation_history,
                )
            elif initial_model.startswith("o"):
                initial_response = await openai_transfer_client2.chat.completions.create(
                    model=initial_model,
                    # messages=conversation_history,
                    messages=[{"role": "user", "content": prompt}],
                )
            else:
                raise ValueError("Unsupported initial model type")

            preliminary_result = initial_response.choices[0].message.content.strip()
            conversation_history.append({"role": "assistant", "content": preliminary_result})

            print("----------------------------------------------")

            print(f"Initial Response: {preliminary_result}")  # 记录初始响应
            print("--------------------------------------------------------")

            print(f"initial_conversation_history:{conversation_history}")
            print("----------------------------------------------")
            # 步骤2：反馈模型调用
            feedback_prompt = Feedback_prompt.format(preliminary_result)
            feedback_messages = [
                {"role": "system", "content": "You are a helpful assistant providing feedback."},
                {"role": "user", "content": feedback_prompt},
            ]
            # conversation_history.extend(feedback_messages)

            print(f"feedback_prompt: {feedback_prompt}")
            print("--------------------------------------------------------")
            print(f"feedback_prompt_conversation_history: {conversation_history}")
            print("--------------------------------------------------------")

            if feedback_model.startswith("gpt"):
                feedback_response = await openai_transfer_client.chat.completions.create(
                    model=feedback_model,
                    messages=feedback_messages,
                )
            elif feedback_model.startswith("o"):
                feedback_response = await openai_transfer_client2.chat.completions.create(
                    model=feedback_model,
                    # messages=feedback_messages,
                    messages=[{"role": "user", "content": feedback_prompt}],
                )
            else:
                raise ValueError("Unsupported feedback model type")

            feedback_result = feedback_response.choices[0].message.content.strip()
            # conversation_history.append({"role": "assistant", "content": feedback_result})

            print(f"Feedback result: {feedback_result}")  # 记录反馈响应
            print("--------------------------------------------------------")

            # 步骤3：最终模型调用
            final_prompt = FinalFeedback_prompt.format(copy=preliminary_result, optimate_copy=feedback_result,
                                                       product=input_data.get('productIntroduction', '')
                                                       )

            final_messages = conversation_history + [{"role": "user", "content": final_prompt}]
            conversation_history.extend(final_messages)

            print(f"final_prompt: {final_prompt}")

            if initial_model.startswith("gpt"):
                final_response = await openai_transfer_client.chat.completions.create(
                    model=initial_model,
                    messages=final_messages,
                )
            elif initial_model.startswith("deepseek"):
                final_response = await deepseek_client.chat.completions.create(
                    model=initial_model,
                    messages=final_messages,
                    temperature=1.25,
                )
            elif initial_model.startswith("fine") or initial_model.startswith("ft:"):
                final_response = await ft_client.chat.completions.create(
                    model=initial_model,
                    messages=final_messages,
                )
            elif initial_model.startswith("o"):
                final_response = await openai_transfer_client2.chat.completions.create(
                    model=initial_model,
                    messages=[{"role": "user", "content": final_prompt}],
                )
            else:
                raise ValueError("Unsupported final model type")

            print("--------------------------------------------------------")
            # 记录最终模型和消息
            print(f"final_messages: {final_messages}")
            print("--------------------------------------------------------")

            final_result = final_response.choices[0].message.content.strip()
            conversation_history.append({"role": "assistant", "content": final_result})

            print(f"Final Result: {final_result}", flush=True)  # 记录最终结果
            print("--------------------------------------------------------")

            return final_result, initial_response, feedback_response, final_response

        except asyncio.TimeoutError:
            logger.error("Timeout error in generate_with_feedback.")
            await self.send_message("Request timed out. Please try again later.", target=1, status=0)

        except Exception as e:
            logger.error(f'Error in generate_with_feedback: {e}')
            raise e

    # 生成公众号大纲
    async def get_outline(self, input_data, model="deepseek-chat"):

        try:
            print("outline_prompt:", outline_prompt)
            content = outline_prompt.format(product_name=input_data.get("productName", ""),
                                            product_introduction=input_data.get('productIntroduction', ''),
                                            product_selling_point=input_data.get(
                                                'productSellingPoints', ''),
                                            copy_theme=input_data.get("copytheme2", ""), )

            print("大纲提示词内容:", content)

            messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": content},
            ]

            print("对话内容messages", messages)

            response = ""
            if model.startswith("gpt"):
                response = await openai_transfer_client.chat.completions.create(
                    model=model,
                    messages=messages,

                )
            elif model.startswith("deepseek"):
                response = await deepseek_client.chat.completions.create(
                    model=model,
                    messages=messages,

                )
            elif model.startswith("o1"):
                response = await openai_transfer_client2.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "user", "content": content},
                    ]
                )
            else:
                raise ValueError("Unsupported model type")

            print("response 微信公众号大纲:", response)

            # 处理响应内容
            temp = response.choices[0].message.content.replace("\n", "<br>")
            target = 2  # 对应 get_outline 的 target
            await self.send(json.dumps({"message": temp, "target": target}))
            await self.send(json.dumps({"status": 1, "target": 2}))
            try:
                # 计算token
                prompt_token = num_tokens_from_messages(messages, model)
                output_token = num_tokens_from_messages([{"role": "system", "content": temp}], model) - 7
                await aupdate_usage(self.scope["user"], model, prompt_token, output_token)
            except Exception as e:
                logger.error('style_content Update_usage error: %s', str(e))
        except Exception as e:
            logger.error('style_content consumer error: %s', str(e))
            await self.send(json.dumps({"status": 0, "target": 2}))

    # 文案检查
    async def get_checkCopywriting(self, input_data):
        try:
            # 1. 获取用户提交的文案内容和参考资料
            copywriting_content = input_data.get("copywritingContent", "")
            reference_content = input_data.get("referenceContent", "")

            # 2. 构建检查文案的提示 (prompt)
            check_prompt = f"""
            请检查以下文案内容，检查其语法、拼写、流畅性，并提供修改建议：
            
            文案内容：{copywriting_content}
            
            参考资料：{reference_content}
            """

            # 3. 创建对话历史，给定系统和用户的消息
            conversation_history = [
                {"role": "system", "content": "你是一个专业的文案检查工具，帮助检查文案的语法和质量。"},
                {"role": "user", "content": check_prompt},
            ]

            # 4. 调用 OpenAI API 进行文案检查
            response = ""
            model = "gpt-4o"  # 默认使用 GPT-4，可以根据需要指定模型
            if model.startswith("gpt"):
                response = await openai_transfer_client.chat.completions.create(
                    model=model,
                    messages=conversation_history,
                )
            elif model.startswith("deepseek"):
                response = await deepseek_client.chat.completions.create(
                    model=model,
                    messages=conversation_history,
                )
            elif model.startswith("o1"):
                response = await openai_transfer_client2.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": check_prompt}],
                )
            else:
                raise ValueError("不支持的模型类型")

            print("API全部回复：", response)

            # 获取修改建议
            if hasattr(response, 'choices') and len(response.choices) > 0:
                check_result = response.choices[0].message.content.strip()  # 获取修改建议
            else:
                check_result = "无法获取文案检查结果，请稍后再试"

            # 5. 处理响应内容
            # check_result = response.choices[0].message.content.strip()  # 获取修改建议
            print("--------------------------------------------------------")
            print("文案检查响应:", check_result)
            print("--------------------------------------------------------")

            check_result = check_result.replace("\n", "<br>").replace("\r", "")

            # 6. 将结果返回给前端
            target = 5  # 用于标识文案检查的请求来源

            message_data = {"message": check_result, "target": target}

            print("发送给前端的数据：", message_data)
            print("--------------------------------------------------------")

            await self.send(json.dumps(message_data))

            # await self.send(json.dumps({"message": check_result, "target": target}))
            await self.send(json.dumps({"status": 1, "target": 5}))

            # 7. 计算token使用量
            try:
                prompt_token = num_tokens_from_messages(conversation_history, model)
                output_token = num_tokens_from_messages([{"role": "system", "content": check_result}], model) - 7
                await aupdate_usage(self.scope["user"], model, prompt_token, output_token)
            except Exception as e:
                logger.error('checkCopywriting Update_usage error: %s', str(e))

        except Exception as e:
            # 发生错误时，发送错误信息
            logger.error(f"文案检查过程中发生错误: {e}")
            await self.send(json.dumps({"status": 0, "target": 5}))

    # 优化文案
    async def optimize(self, input_data, model="deepseek-chat"):
        try:
            content = optimize_prompt.format(input_data['formattedText'])
            messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": content},
            ]
            client = AsyncOpenAI(
                api_key=deepseek_api_key,
                base_url="https://api.deepseek.com/"
            )
            response = await client.chat.completions.create(
                model=model,
                messages=messages,

                temperature=1.25,
            )
            print(f"页面的response: {response}")
            temp = response.choices[0].message.content.replace("\n", "<br>")
            await self.send(json.dumps({"message": temp, "target": 3}))
            await self.send(json.dumps({"status": 1, "target": 3}))
            try:
                pass
            # 此处需要补充计算tokens的步骤
            except Exception as e:
                logger.error('style_content Update_usage error: %s', str(e))
        except Exception as e:
            logger.error('style_content consumer error: %s', str(e))
            await self.send(json.dumps({"status": 0, "target": 3}))

    async def chat(self, input_data, model="deepseek-chat"):
        try:
            chat_list = input_data["chat_list"]
            chat_list.insert(0, {"role": "system",
                                 "content": "You are a helpful assistant."})
            client = AsyncOpenAI(
                # defaults to os.environ.get("OPENAI_API_KEY")
                api_key=deepseek_api_key,
                base_url="https://api.deepseek.com/"
            )
            response = await client.chat.completions.create(
                model=model,
                messages=chat_list,
                stream=True,

            )

            # 处理响应内容
            # temp = response.choices[0].message.content.replace("\n", "<br>")
            # await self.send(json.dumps({"message": temp, "target": 4}))
            # await self.send(json.dumps({"status": 1, "target": 4}))
            temp = ""
            async for contents in response:
                if contents.choices[0].delta.content is not None:
                    temp += contents.choices[0].delta.content
                    new = contents.choices[0].delta.content.replace("\n", "<br>")
                    await self.send(json.dumps({"message": new, "target": 4}))

            await self.send(json.dumps({"status": 1, "target": 4}))
            try:
                pass
            # 此处需要补充计算tokens的步骤
            except Exception as e:
                logger.error('style_content Update_usage error: %s', str(e))
        except Exception as e:
            logger.error('style_content consumer error: %s', str(e))
            await self.send(json.dumps({"status": 0, "target": 4}))

    # 文案平台转换
    async def platform_conversion(self, input_data, model="gpt-4o"):
        logger.info("文案平台转换处理方法开始")
        logger.info("input_data: %s", input_data)
        """
        文案平台转换处理方法
        """
        try:
            # 获取原始文案内容和目标平台
            original_content = input_data.get("originalContent", "").strip()
            target_platform = input_data.get("targetPlatform", "")
            platform_key = input_data.get("platformKey", "")
            
            if not original_content:
                await self.send(json.dumps({"message": "请输入要转换的文案内容", "target": 6}))
                await self.send(json.dumps({"status": 0, "target": 6}))
                return
            
            if not target_platform:
                await self.send(json.dumps({"message": "请选择目标平台", "target": 6}))
                await self.send(json.dumps({"status": 0, "target": 6}))
                return
            
            print(f"文案平台转换请求 - 原始内容: {original_content[:50]}...")
            print(f"目标平台: {target_platform}")
            print(f"平台键: {platform_key}")
            
            # 构建平台特定的转换提示词
            platform_prompts = {
                'weibo': '请将以下文案转换为适合微博平台的风格：字数控制在140字以内，语言简洁有力，适合快速传播，可以使用相关话题标签和表情符号。',
                'xiaohongshu': '请将以下文案转换为适合小红书平台的风格：使用年轻化、生活化的语言，加入适当的表情符号，突出产品特点和使用体验，字数适中。',
                'wechat': '请将以下文案转换为适合微信公众号的风格：语言相对正式但亲和，结构清晰，可以适当延展内容，注重可读性和用户体验。',
                'douyin': '请将以下文案转换为适合抖音平台的风格：语言活泼有趣，富有节奏感，容易记忆，适合短视频内容，可以使用流行词汇和表情符号。'
            }
            
            conversion_prompt = platform_prompts.get(platform_key, f"请将以下文案转换为适合{target_platform}平台的风格：")
            
            # 构建完整的转换提示
            full_prompt = f"""
{conversion_prompt}

原始文案：
{original_content}

请直接输出转换后的文案，不需要额外的说明。
"""
            
            # 构建对话消息
            conversation_history = [
                {"role": "system", "content": f"你是一个专业的文案转换助手，擅长将文案转换为不同平台的风格。"},
                {"role": "user", "content": full_prompt},
            ]
            
            print(f"转换提示词: {full_prompt}")
            print("----------------------------------------------")
            
            # 调用AI模型进行转换
            response = ""
            if model.startswith("gpt"):
                response = await openai_transfer_client.chat.completions.create(
                    model=model,
                    messages=conversation_history,
                )
            elif model.startswith("deepseek"):
                response = await deepseek_client.chat.completions.create(
                    model=model,
                    messages=conversation_history,
                )
            elif model.startswith("o1"):
                response = await openai_transfer_client2.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": full_prompt}],
                )
            else:
                raise ValueError("不支持的模型类型")
            
            # 获取转换结果
            if hasattr(response, 'choices') and len(response.choices) > 0:
                converted_content = response.choices[0].message.content.strip()
            else:
                converted_content = "转换失败，请稍后重试"
            
            print(f"转换结果: {converted_content}")
            print("----------------------------------------------")
            
            # 将结果格式化并发送到前端
            formatted_content = converted_content.replace("\n", "<br>")
            
            # 发送转换结果
            await self.send(json.dumps({"message": formatted_content, "target": 6}))
            
            # 发送成功状态
            await self.send(json.dumps({"status": 1, "target": 6}))
            
            # 计算token使用量
            try:
                prompt_token = num_tokens_from_messages(conversation_history, model)
                output_token = num_tokens_from_messages([{"role": "system", "content": converted_content}], model) - 7
                await aupdate_usage(self.scope["user"], model, prompt_token, output_token)
            except Exception as e:
                logger.error('platform_conversion Update_usage error: %s', str(e))
                
        except Exception as e:
            # 发生错误时，发送错误信息
            logger.error(f"文案平台转换过程中发生错误: {e}")
            await self.send(json.dumps({"message": f"转换失败: {str(e)}", "target": 6}))
            await self.send(json.dumps({"status": 0, "target": 6}))

    # 文字转图片查询分析
    async def wordtoimages_query(self, input_data, model="gpt-4o"):
        logger.info("文字转图片查询分析处理方法开始")
        logger.info("input_data: %s", input_data)
        """
        文字转图片查询分析处理方法
        """
        try:
            # 获取文字内容和图片数据
            text_content = input_data.get("textContent", "").strip()
            uploaded_images = input_data.get('uploadedImages', [])
            prompt = input_data.get('imageAnalysisRequest', '分析图片内容并结合文字描述提供详细的分析结果')

            logger.info(f"收到文字内容: {text_content}")
            logger.info(f"收到图片数量: {len(uploaded_images)}")

            if not text_content and not uploaded_images:
                await self.send(json.dumps({"message": "请输入文字描述或上传图片", "target": 7}))
                await self.send(json.dumps({"status": 0, "target": 7}))
                return

            # 构建分析提示词
            analysis_prompt = f"""
作为一个专业的图像和文字分析师，请根据以下信息进行详细分析：

文字描述：
{text_content if text_content else "无文字描述"}

请从以下几个方面进行分析：
1. 内容理解：理解文字描述的核心含义和关键信息
2. 视觉元素：如果有图片，分析图片中的视觉元素、色彩、构图等
3. 关联分析：分析文字和图片之间的关联性和一致性
4. 创意建议：基于分析结果，提供创意建议和改进方案
5. 应用场景：分析适合的应用场景和目标受众

请提供详细、专业且有建设性的分析结果。
"""

            # 如果有图片，先处理图片
            image_analysis_result = ""
            if uploaded_images:
                proceeded_images = await self.process_images(uploaded_images)
                if proceeded_images:
                    result = await analyzer.analyze_base64_images(proceeded_images, prompt)
                    if result["success"]:
                        image_analysis_result = result['result']
                        logger.info(f"图片分析结果: {image_analysis_result}")
                    else:
                        logger.error("图片分析失败:", result["error"])
                        image_analysis_result = "图片分析失败，请重试"

            # 构建完整的分析内容
            if image_analysis_result:
                full_analysis_prompt = f"""
{analysis_prompt}

图片分析结果：
{image_analysis_result}

请结合以上图片分析结果和文字描述，提供综合性的分析报告。
"""
            else:
                full_analysis_prompt = analysis_prompt

            # 构建对话消息
            conversation_history = [
                {"role": "system", "content": "你是一个专业的图像和文字分析师，擅长分析文字内容和图片的关联性，并提供专业的建议。"},
                {"role": "user", "content": full_analysis_prompt},
            ]

            print(f"分析提示词: {full_analysis_prompt}")
            print("----------------------------------------------")

            # 调用AI模型进行分析
            response = ""
            if model.startswith("gpt"):
                response = await openai_transfer_client.chat.completions.create(
                    model=model,
                    messages=conversation_history,
                )
            elif model.startswith("deepseek"):
                response = await deepseek_client.chat.completions.create(
                    model=model,
                    messages=conversation_history,
                )
            elif model.startswith("o1"):
                response = await openai_transfer_client2.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": full_analysis_prompt}],
                )
            else:
                raise ValueError("不支持的模型类型")

            # 获取分析结果
            analysis_result = response.choices[0].message.content.strip()

            print(f"分析结果: {analysis_result}")
            print("----------------------------------------------")

            # 将结果格式化并发送到前端
            formatted_content = analysis_result.replace("\n", "<br>")

            # 发送分析结果
            await self.send(json.dumps({"message": formatted_content, "target": 7}))

            # 发送成功状态
            await self.send(json.dumps({"status": 1, "target": 7}))

            # 计算token使用量
            try:
                prompt_token = num_tokens_from_messages(conversation_history, model)
                output_token = num_tokens_from_messages([{"role": "system", "content": analysis_result}], model) - 7
                await aupdate_usage(self.scope["user"], model, prompt_token, output_token)
            except Exception as e:
                logger.error('wordtoimages_query Update_usage error: %s', str(e))

        except Exception as e:
            # 发生错误时，发送错误信息
            logger.error(f"文字转图片查询分析过程中发生错误: {e}")
            await self.send(json.dumps({"message": f"分析失败: {str(e)}", "target": 7}))
            await self.send(json.dumps({"status": 0, "target": 7}))
