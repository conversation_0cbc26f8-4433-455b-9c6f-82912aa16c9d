from test import SimpleProcessor

processor = SimpleProcessor()
#your_json_text = """[{"semantic_unit": "The bullish ETH trading summary highlights favorable conditions such as potential Fed rate cuts in 2024, Bitcoin ETF approvals boosting crypto sentiment, Ethereum's EIP-4844 upgrade reducing L2 costs, rising staking demand, and fee-burning mechanisms making ETH deflationary.", "entities": ["ETH", "2024", "FED", "BITCOIN ETF", "EIP-4844", "L2", "STAKING DEMAND", "FEE-BURNING MECHANISM"], "relationships": ["FED, potential rate cuts in, 2024", "BITCOIN ETF, approvals boosting, CRYPTO MARKET SENTIMENT", "EIP-4844, expected in, 2024", "EIP-4844, reducing costs for, L2", "STAKING DEMAND, rising with, ETH", "FEE-BURNING MECHANISM, making, ETH deflationary"]}, {"semantic_unit": "ETH shows strong on-chain and technicals, including a rising ETH/BTC ratio, holding key support levels, and growing institutional interest such as BlackRock's Ethereum ETF application.", "entities": ["ETH", "ETH/BTC RATIO", "KEY SUPPORT LEVELS", "BLACKROCK", "ETHEREUM ETF"], "relationships": ["ETH/BTC RATIO, showing strength for, ETH", "ETH, holding, KEY SUPPORT LEVELS", "BLACKROCK, applied for, ETHEREUM ETF"]}, {"semantic_unit": "Upcoming catalysts for ETH include Spot Ethereum ETF decisions in May 2024 and Layer-2 ecosystem growth with platforms like Arbitrum, Optimism, and zkSync driving utility.", "entities": ["ETH", "SPOT ETHEREUM ETF", "2024-05", "LAYER-2 ECOSYSTEM", "ARBITRUM", "OPTIMISM", "ZKSYNC"], "relationships": ["SPOT ETHEREUM ETF, decisions expected in, 2024-05", "LAYER-2 ECOSYSTEM, growth driven by, ARBITRUM, OPTIMISM, ZKSYNC"]}, {"semantic_unit": "The proposed trading action suggests accumulating ETH at key support levels ($3,200\u2013$3,400), using DCA, and optionally leveraging with tight stop-losses. Targets include $3,800\u2013$4,000 short-term and $4,500\u2013$5,000 mid-term.", "entities": ["ETH", "KEY SUPPORT ZONE", "DCA", "LEVERAGE", "STOP-LOSSES", "TARGETS"], "relationships": ["ETH, accumulate in, KEY SUPPORT ZONE ($3,200\u2013$3,400)", "DCA, used to mitigate, VOLATILITY RISK", "LEVERAGE, optional with, STOP-LOSSES (~$3,000)", "ETH, targets for, TARGETS ($3,800\u2013$5,000)"]}, {"semantic_unit": "Risk management includes monitoring Bitcoin dominance and regulatory FUD. Long-term strategies involve staking ETH for APY and hedging with puts if volatility spikes.", "entities": ["RISK MANAGEMENT", "BITCOIN DOMINANCE", "REGULATORY FUD", "ETH", "STAKING", "APY", "HEDGING", "PUTS", "VOLATILITY"], "relationships": ["RISK MANAGEMENT, includes monitoring, BITCOIN DOMINANCE", "REGULATORY FUD, watch for, ETH", "ETH, staking for, APY", "HEDGING, using, PUTS for VOLATILITY"]}, {"semantic_unit": "The conclusion advises buying ETH on weakness, scaling into strength, and trailing stops above $3,500, given ETH's strong fundamentals and upcoming catalysts.", "entities": ["ETH", "BUY WEAKNESS", "SCALE INTO STRENGTH", "TRAILING STOPS", "$3,500"], "relationships": ["ETH, strategy to, BUY WEAKNESS", "ETH, scale into, STRENGTH", "TRAILING STOPS, set above, $3,500"]}]
#"""
your_json_text = """
[
  {
    "semantic_unit": "链上分析师代理分析了以太坊的链上数据，包括强劲的开盘价（2080.85美元）、高燃气费价格和活跃的网络交易量，并基于MACD指标发出买入信号。",
    "entities": ["链上分析师代理", "以太坊", "2080.85美元", "燃气费价格", "MACD指标", "买入信号"],
    "relationships": [
      "链上分析师代理, 分析, 以太坊链上数据",
      "链上分析师代理, 发出, 买入信号",
      "以太坊, 显示, 高燃气费价格",
      "MACD指标, 支持, 买入信号"
    ]
  },
  {
    "semantic_unit": "新闻分析师代理指出加密货币市场整体呈积极趋势，以太坊表现领先，但提到市场波动性和潜在盘整风险。",
    "entities": ["新闻分析师代理", "加密货币市场", "以太坊", "市场波动性", "盘整风险"],
    "relationships": [
      "新闻分析师代理, 报告, 市场趋势",
      "以太坊, 表现, 领先",
      "市场波动性, 可能引发, 盘整风险"
    ]
  },
  {
    "semantic_unit": "复盘分析师代理总结近期交易不活跃，建议保守策略，并强调需关注技术指标和全球经济事件。",
    "entities": ["复盘分析师代理", "交易不活跃", "保守策略", "技术指标", "全球经济事件"],
    "relationships": [
      "复盘分析师代理, 建议, 保守策略",
      "交易不活跃, 反映, 市场中性",
      "全球经济事件, 影响, 加密货币市场"
    ]
  },
  {
    "semantic_unit": "看涨代理综合链上数据和市场情绪，给出70%信心的买入建议，认为以太坊生态增长潜力强劲。",
    "entities": ["看涨代理", "链上数据", "市场情绪", "70%信心", "买入建议", "以太坊生态"],
    "relationships": [
      "看涨代理, 引用, 链上数据",
      "看涨代理, 评估, 市场情绪",
      "看涨代理, 提出, 买入建议",
      "以太坊生态, 具有, 增长潜力"
    ]
  },
  {
    "semantic_unit": "看跌代理关注高燃气费、市场波动性和盘整风险，给出60%信心的卖出建议。",
    "entities": ["看跌代理", "高燃气费", "市场波动性", "盘整风险", "60%信心", "卖出建议"],
    "relationships": [
      "看跌代理, 警告, 高燃气费问题",
      "看跌代理, 担忧, 盘整风险",
      "看跌代理, 提出, 卖出建议"
    ]
  },
  {
    "semantic_unit": "风险代理评估市场为中等风险，建议15%仓位，并强调分散投资和止损策略。",
    "entities": ["风险代理", "中等风险", "15%仓位", "分散投资", "止损策略"],
    "relationships": [
      "风险代理, 评估, 中等风险",
      "风险代理, 建议, 15%仓位",
      "分散投资, 用于, 降低风险",
      "止损策略, 应对, 市场波动"
    ]
  },
  {
    "semantic_unit": "决策代理综合所有分析，最终决定买入以太坊，但提示长期需保守策略。",
    "entities": ["决策代理", "买入", "以太坊", "长期保守策略"],
    "relationships": [
      "决策代理, 整合, 看涨/看跌/风险分析",
      "决策代理, 决定, 买入",
      "长期保守策略, 针对, 高波动性"
    ]
  }
]"""
processor.process_json_text(your_json_text)  # your_json_text 是你提供的JSON字符串
processor.print_stats()
processor.print_details()

from typing import Dict, List, Set
from dataclasses import dataclass


def get_all_unique_entities(processor: SimpleProcessor) -> List[str]:
    """获取所有唯一的实体内容"""
    entity_contents = {entity.content for entity in processor.N}
    return sorted(list(entity_contents))


def get_entity_relationships(processor: SimpleProcessor, entity_content: str) -> List[str]:
    """获取与特定实体相关的所有关系"""
    return [rel.content for rel in processor.R if entity_content in rel.content]


def get_entity_semantic_units(processor: SimpleProcessor, entity_content: str) -> List[str]:
    """获取包含特定实体的所有语义单元"""
    return [sem.content for sem in processor.S if entity_content in sem.content]


def analyze_entity(processor: SimpleProcessor, entity_content: str) -> str:
    """分析单个实体的所有相关信息"""
    relationships = get_entity_relationships(processor, entity_content)
    semantic_units = get_entity_semantic_units(processor, entity_content)

    return f"""
Entity: {entity_content}

Related Relationships ({len(relationships)}):
{chr(10).join(f"- {r}" for r in relationships)}

Semantic Context ({len(semantic_units)}):
{chr(10).join(f"- {s}" for s in semantic_units)}
"""


def main(processor: SimpleProcessor):
    print("Analyzing entities from the processor...")
    entities = get_all_unique_entities(processor)
    print(f"\nFound {len(entities)} unique entities")

    for entity in entities:
        print("\n" + "=" * 80)
        print(analyze_entity(processor, entity))


if __name__ == "__main__":
    main(processor)