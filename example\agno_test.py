from agno.agent import Agent
from agno.models.deepseek import DeepSeek
from agno.tools.reasoning import ReasoningTools


agent = Agent(
    model=DeepSeek(id="deepseek-chat",api_key="***********************************"),
    # common_tools=[
    #     ReasoningTools(add_instructions=True),
    #
    # ],
    # instructions=[
    #     "Use tables to display data",
    #     "Only output the report, no other text",
    # ],
    markdown=True,
)
agent.print_response("你是什么模型", stream=True, show_full_reasoning=True, stream_intermediate_steps=True)