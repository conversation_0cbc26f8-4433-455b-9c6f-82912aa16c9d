import pytz
from django.shortcuts import render
from style_content.models import CreatedContent
from style_content.tools.load_config import Config
# Create your views here.
config = Config()
try:
    config_data = config.get_config()
    print(f"DEBUG: config_data type: {type(config_data)}")
    print(f"DEBUG: config_data keys: {config_data.keys() if isinstance(config_data, dict) else 'Not a dict'}")

    models = config_data["support_models"]
    platforms = config_data["platform_list"]
    brands = config_data["brand_list"]

    print(f"DEBUG: models loaded: {models}")
    print(f"DEBUG: models type: {type(models)}")
except Exception as e:
    print(f"ERROR: 加载配置时出错: {e}")
    # 使用默认值
    models = [{'name': 'gpt-4o-mini'}, {'name': 'deepseek-chat'}]
    platforms = [{'name': '小红书'}, {'name': '公众号'}]
    brands = [{'name': 'LV'}, {'name': '<PERSON>'}]

def copywritting(request):
    context = {'models': models, 'platforms': platforms, 'brands': brands}
    return render(request, "index/copywritting.html", context)


def change_platform(request):
    return render(request, "index/change_platform.html")

def checkCopywriting(request):
    return render(request, "index/checkCopywriting.html")

def optimize(request):
    options = []
    if request.user.is_authenticated:
        records = CreatedContent.objects.filter(userid=request.user.id).values('id','Final_output', 'create_time')

        for i in records:
            beijing_tz = pytz.timezone('Asia/Shanghai')
            dt_beijing = i["create_time"].astimezone(beijing_tz)
            beijing_time_str = dt_beijing.strftime('%Y-%m-%d %H:%M:%S')
            options.append({"id": i["id"], "time": beijing_time_str, "info": i["Final_output"]})
    context = {"options":options}
    return render(request, "index/optimize.html", context)

def chat(request):
    return render(request, "index/chat.html")

def wordtoimages_quary(request):
    # 添加调试信息
    print(f"DEBUG: models type: {type(models)}")
    print(f"DEBUG: models content: {models}")

    # 确保models是正确的格式
    try:
        if isinstance(models, list) and all(isinstance(m, dict) and 'name' in m for m in models):
            context = {'models': models}
        else:
            # 如果models格式不正确，使用默认值
            print("WARNING: models格式不正确，使用默认值")
            context = {'models': [{'name': 'gpt-4o-mini'}, {'name': 'deepseek-chat'}]}
    except Exception as e:
        print(f"ERROR: 处理models时出错: {e}")
        context = {'models': [{'name': 'gpt-4o-mini'}, {'name': 'deepseek-chat'}]}

    return render(request, "index/wordtoimages_quary.html", context)

def demands_for_stars_birthday(request):
    """处理明星生日需求上传页面的请求"""
    context = {'models': models}
    return render(request, "index/demands_for_stars_birthday.html", context)




