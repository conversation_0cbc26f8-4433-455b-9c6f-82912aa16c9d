from test import SimpleProcessor

def main():
    # 创建处理器实例
    processor = SimpleProcessor()

    # 测试JSON文本
    your_json_text = """[
      {
        "semantic_unit": "2024年美联储可能降息和比特币ETF的批准提振了加密货币市场的整体情绪，为ETH上涨提供了宏观顺风。",
        "entities": [
          "2024",
          "美联储",
          "降息",
          "比特币ETF",
          "加密货币市场",
          "ETH"
        ],
        "relationships": [
          "美联储, 可能降息, 2024",
          "比特币ETF, 批准, 加密货币市场",
          "加密货币市场, 提振, ETH"
        ]
      },
      {
        "semantic_unit": "以太坊基础中的EIP-4844（Proto-Danksharding）预计于2024年推出，抵押需求上升，费用燃烧机制使ETH在高活动期出现通货紧缩。",
        "entities": [
          "EIP-4844（Proto-Danksharding）",
          "2024",
          "抵押",
          "ETH",
          "费用燃烧机制",
          "通货紧缩"
        ],
        "relationships": [
          "EIP-4844（Proto-Danksharding）, 预计推出, 2024",
          "抵押, 需求上升, ETH",
          "费用燃烧机制, 导致, 通货紧缩"
        ]
      },
      {
        "semantic_unit": "ETH/BTC比率显示强劲，ETH持有关键支撑位，机构兴趣增长，如贝莱德的以太坊ETF应用程序。",
        "entities": [
          "ETH/BTC比率",
          "ETH",
          "关键支撑位",
          "机构兴趣",
          "贝莱德",
          "以太坊ETF"
        ],
        "relationships": [
          "ETH/BTC比率, 显示强劲, ETH",
          "ETH, 持有, 关键支撑位",
          "机构兴趣, 增长, ETH",
          "贝莱德, 申请, 以太坊ETF"
        ]
      }
    ]"""

    # 处理JSON文本（自动去重）
    processor.process_json_text(your_json_text)

    # 打印统计信息
    processor.print_stats()

    # 打印详细信息
    processor.print_details()

    # 获取所有唯一实体内容
    entity_contents = [entity.content for entity in processor.N]
    print(f"\n找到 {len(entity_contents)} 个唯一实体")

    # 使用内置方法分析每个实体
    for entity_content in entity_contents:
        print("\n" + "=" * 80)
        print(processor.analyze_entity(entity_content))

if __name__ == "__main__":
    main()
