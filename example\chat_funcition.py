import json
import logging

from openai import AsyncOpenAI
from style_content.tools.load_config import Config
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer

config = Config()

logger = logging.getLogger(__name__)
deepseek_api_key = config.get_config()["deepseek_apikey"]


async def send(self, text_data=None, bytes_data=None, close=False):
    """
    Sends a reply back down the WebSocket
    """
    if text_data is not None:
        await super().send({"type": "websocket.send", "text": text_data})
    elif bytes_data is not None:
        await super().send({"type": "websocket.send", "bytes": bytes_data})
    else:
        raise ValueError("You must pass one of bytes_data or text_data")
    if close:
        await self.close(close)

async def chat(input_data, model="deepseek-chat"):
    try:
        chat_list = input_data["chat_list"]
        chat_list.insert(0, {"role": "system",
                             "content": "You are a helpful assistant."})
        client = AsyncOpenAI(
            # defaults to os.environ.get("OPENAI_API_KEY")
            api_key=deepseek_api_key,
            base_url="https://api.deepseek.com/"
        )
        response = await client.chat.completions.create(
            model=model,
            messages=chat_list,
        )

        # 处理响应内容
        temp = response.choices[0].message.content.replace("\n", "<br>")
        await send(json.dumps({"message": temp, "target": 4}))
        await send(json.dumps({"status": 1, "target": 4}))
        try:
            pass
        # 此处需要补充计算tokens的步骤
        except Exception as e:
            logger.error('style_content Update_usage error: %s', str(e))
    except Exception as e:
        logger.error('style_content consumer error: %s', str(e))
        await send(json.dumps({"status": 0, "target": 4}))


input_data = {
    "chat_list": [
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "user", "content": "以下是一个产品介绍手册中的内容，你需要将产品的主要信息整理出来以便后续产品宣传文案的撰写。你的回答中只需要包含产品相关信息，不需要开头语和结尾语。<产品手册>"},
    ]
}
# 使用 asyncio.run() 运行异步函数
asyncio.run(chat(input_data, model="deepseek-chat"))
