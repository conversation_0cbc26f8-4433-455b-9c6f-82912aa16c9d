import networkx as nx
import itertools
import math
import shap
import numpy as np
from myerson import MyersonCalculator, <PERSON>onSample<PERSON>


def parse_graph_edges(edges_str):
    """解析图的边缘结构"""
    try:
        # 安全地评估字符串作为Python字面量
        graph_list = eval(edges_str)
        if not isinstance(graph_list, list):
            raise ValueError("输入不是列表。")

        parsed_edges = []
        for edge in graph_list:
            if isinstance(edge, tuple) and len(edge) == 2:
                parsed_edges.append(tuple(str(item).strip() for item in edge))

        return parsed_edges
    except Exception as e:
        print(f"解析图字符串时出错: {e}")
        return []


def parse_coalition_value(coalition_str, value):
    """将字符串格式的联盟转换为元组"""
    # 将例如 ('a','b','c') 解析为 ('a', 'b', 'c')
    # 去除括号和空格，然后分割
    if coalition_str == '()':
        return tuple(), value
    coalition_str = coalition_str.strip("()").replace("'", "").replace('"', '')
    players = tuple(p.strip() for p in coalition_str.split(','))
    return players, value


def parse_coalition_values(values_str):
    """解析联盟值字典"""
    # 去除最外层的花括号
    values_str = values_str.strip("{}")
    if not values_str:
        return {}

    result = {}
    # 找到所有键值对
    entries = []
    start = 0
    level = 0
    i = 0

    while i < len(values_str):
        char = values_str[i]
        # 处理键部分
        if char == '(' and (i == 0 or values_str[i - 1] != "'"):
            if level == 0:
                start = i
            level += 1
        elif char == ')' and (i == len(values_str) - 1 or values_str[i + 1] != "'"):
            level -= 1
            if level == 0:
                # 寻找冒号
                j = i + 1
                while j < len(values_str) and values_str[j] != ':':
                    j += 1

                if j < len(values_str):
                    # 寻找值的结束位置
                    k = j + 1
                    while k < len(values_str) and values_str[k] not in [',']:
                        k += 1

                    key_str = values_str[start:i + 1]
                    value_str = values_str[j + 1:k].strip()

                    try:
                        value = float(value_str)
                        coalition, _ = parse_coalition_value(key_str, 0)
                        result[coalition] = value
                    except ValueError:
                        # 处理值不是数字的情况
                        pass

                    i = k
                    if i < len(values_str) and values_str[i] == ',':
                        i += 1
                    continue

        i += 1

    # 添加空集的值
    result[tuple()] = 0.0

    return result


def create_custom_coalition_function(custom_values):
    """创建基于自定义值的联盟函数"""

    def custom_coalition_function(coalition, nx_graph=None):
        # 将列表或集合转换为元组，以便作为字典键
        coalition_tuple = tuple(sorted(coalition))
        # 返回自定义值，如果不存在则返回0
        return custom_values.get(coalition_tuple, 0.0)

    return custom_coalition_function


def calculate_traditional_shapley(coalition_values, players):
    """计算传统的Shapley值（不考虑图的连通性）
    
    Args:
        coalition_values: 一个字典，键为联盟（元组形式），值为联盟的价值
        players: 玩家列表
    
    Returns:
        一个字典，键为玩家，值为其Shapley值
    """
    # 定义一个函数计算特征函数值
    def characteristic_function(coalition):
        coalition_tuple = tuple(sorted([players[i] for i in range(len(players)) if coalition[i] == 1]))
        return coalition_values.get(coalition_tuple, 0.0)
    
    # 创建一个简单的模型函数，它接受一个布尔数组并返回特征函数值
    def model(x):
        # 将特征转换为0和1的数组
        x_binary = np.round(x).astype(int)
        return np.array([characteristic_function(row) for row in x_binary])
    
    # 创建一个背景数据集
    background_data = np.zeros((1, len(players)))
    
    # 创建一个解释器
    explainer = shap.Explainer(model, background_data)
    
    # 计算Shapley值 - 我们使用一个单一样本，其中所有玩家都参与
    shap_values = explainer(np.ones((1, len(players))))
    
    # 将结果转换为字典
    result = {players[i]: shap_values.values[0, i] for i in range(len(players))}
    
    return result


def find_disconnected_coalitions(players, graph_edges):
    """
    查找给定图中所有不连通的联盟
    
    Args:
        players: 玩家列表
        graph_edges: 图的边列表，每条边是一个元组 (player1, player2)
        
    Returns:
        一个列表，包含所有不连通的联盟（以元组形式）
    """
    # 创建NetworkX图
    G = nx.Graph()
    G.add_nodes_from(players)
    G.add_edges_from(graph_edges)
    
    # 生成所有可能的联盟（大小至少为2的联盟）
    all_coalitions = []
    for size in range(2, len(players) + 1):
        all_coalitions.extend(list(itertools.combinations(players, size)))
    
    # 检查每个联盟是否连通
    disconnected_coalitions = []
    for coalition in all_coalitions:
        # 获取联盟对应的子图
        subgraph = G.subgraph(coalition)
        # 如果子图不是连通的，则添加到不连通联盟列表
        if not nx.is_connected(subgraph):
            disconnected_coalitions.append(coalition)
    
    return disconnected_coalitions


def format_disconnected_coalitions(disconnected_coalitions):
    """
    将不连通联盟列表格式化为字符串
    
    Args:
        disconnected_coalitions: 不连通联盟列表
        
    Returns:
        格式化后的字符串
    """
    if not disconnected_coalitions:
        return "不连通的联盟有：{}"
    
    # 格式化每个联盟
    formatted_coalitions = []
    for coalition in disconnected_coalitions:
        players_str = ','.join(coalition)
        formatted_coalitions.append(f"({players_str})")
    
    # 合并所有联盟
    result = "不连通的联盟有：{" + ', '.join(formatted_coalitions) + "}"
    return result


def main():
    try:
        # 用户输入玩家列表
        players_input = input("请输入玩家列表（用逗号分隔，例如 a,b,c,d,e,f）: ")
        players = [p.strip() for p in players_input.split(',')]

        # 用户输入图的结构
        print("请输入图的结构（例如 [('a','c'), ('a','d'), ('b','c')]），或直接回车使用默认图结构: ")
        graph_input = input()
        if not graph_input:
            # 使用默认图结构的例子
            graph_edges = [('a', 'c'), ('a', 'd'), ('a', 'e'), ('b', 'c'), ('b', 'd'), ('b', 'e'),
                           ('c', 'd'), ('c', 'e'), ('d', 'e'), ('c', 'f'), ('d', 'f'), ('e', 'f')]
        else:
            graph_edges = parse_graph_edges(graph_input)

        # 创建NetworkX图
        G = nx.Graph()
        G.add_nodes_from(players)
        G.add_edges_from(graph_edges)

        # 检查图是否连通
        if not nx.is_connected(G) and len(players) > 1:
            print("警告: 输入的图不是连通的，可能会影响Myerson值的计算。")
        
        # 查找并显示不连通的联盟
        disconnected_coalitions = find_disconnected_coalitions(players, graph_edges)
        print(format_disconnected_coalitions(disconnected_coalitions))

        # 用户输入联盟值
        print("请输入联盟值（以字典格式，例如 {('a',): 5, ('a','b'): 15}），或直接回车使用默认联盟值: ")
        values_input = input()

        # 定义联盟值字典
        coalition_values = {}

        if not values_input:
            # 使用默认联盟值的例子 - 这里使用平方和函数
            for i in range(1, len(players) + 1):
                for coalition in itertools.combinations(players, i):
                    coalition_values[coalition] = float(len(coalition)) ** 2
            coalition_values[tuple()] = 0.0
        else:
            try:
                # 解析用户输入的联盟值
                coalition_values = parse_coalition_values(values_input)
            except Exception as e:
                print(f"解析联盟值时出错: {e}，将使用默认联盟值")
                # 使用默认联盟值
                for i in range(1, len(players) + 1):
                    for coalition in itertools.combinations(players, i):
                        coalition_values[coalition] = float(len(coalition)) ** 2
                coalition_values[tuple()] = 0.0

        # 创建基于自定义值的联盟函数
        custom_coalition_function = create_custom_coalition_function(coalition_values)

        # 使用SHAP库计算传统的Shapley值
        print("\n使用SHAP库计算传统Shapley值...")
        traditional_shapley_values = calculate_traditional_shapley(coalition_values, players)

        # 使用myerson包计算Myerson值
        myerson_calculator = MyersonCalculator(
            graph=G,
            coalition_function=custom_coalition_function
        )
        myerson_values = myerson_calculator.calculate_all_myerson_values()

        # 计算大联盟价值和各种值的总和
        grand_coalition = tuple(sorted(players))
        grand_coalition_value = coalition_values.get(grand_coalition, 0.0)
        shapley_total = sum(traditional_shapley_values.values())
        myerson_total = sum(myerson_values.values())
        
        print(f"\n玩家: {players}")
        print(f"图结构: {graph_edges}")
        print(f"大联盟价值: {grand_coalition_value}")
        print(f"传统Shapley值总和: {shapley_total:.4f}")
        print(f"Myerson值总和: {myerson_total:.4f}")
        
        print("\n传统的Shapley值(使用SHAP库计算):")
        for player, value in traditional_shapley_values.items():
            # 添加除零检查
            if shapley_total != 0:
                percentage = (value / shapley_total) * 100
                print(f"玩家 {player}: {value:.4f} ({percentage:.2f}%)")
            else:
                print(f"玩家 {player}: {value:.4f} (无法计算百分比，总和为零)")
        
        print("\nMyerson值 (使用myerson包计算):")
        for player, value in myerson_values.items():
            # 添加除零检查
            if myerson_total != 0:
                percentage = (value / myerson_total) * 100
                print(f"玩家 {player}: {value:.4f} ({percentage:.2f}%)")
            else:
                print(f"玩家 {player}: {value:.4f} (无法计算百分比，总和为零)")

    except EOFError:
        print("\n输入被中断，程序退出。")
    except Exception as e:
        print(f"\n发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()