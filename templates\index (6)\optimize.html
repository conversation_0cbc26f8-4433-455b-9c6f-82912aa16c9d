{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Editor</title>
    <link rel="{% static 'css/style.css' %}" type="text/css" href="style.css">
</head>

<body>
    <div class="editor-container">
        <div id="editor" contenteditable="true">
            <span id="placeholder">将需要修改的文案粘贴在此处...</span>
        </div>
        <!-- 复制按钮 -->
        <button class="copy-button" onclick="copyEditorContent(this)">
            <img src="{% static 'pic/copy.png' %}" alt="Copy Icon"
                style="height: 19px; vertical-align: middle; margin-right: 2px;">复制
        </button>
        <!-- 弹出按钮 -->
        <button id="editIcon" class="edit-button" style="display: none;"></button>
        <!-- 清空文本按钮 -->
        <button id="CleanText" class="btn btn-primary">清空</button>

        <!-- 弹出窗口 -->
        <div id="popupWindow" class="popup" style="display: none;">
            <div class="popup-header">
                <span class="close">&times;</span>
                <h2>文案修改</h2>
            </div>
            <div class="popup-body">
                选中文字：
                <textarea id="selectText" disabled>在此区域显示选中文字</textarea>
                修改意见：
                <textarea id="inputText" placeholder="输入修改意见"></textarea>
                <div style="display:flex;justify-content: flex-start; align-items: center;">
                    <button id="submit" class="btn btn-primary" style="margin-bottom: 10px">提交</button>
                    <div id="loadingIndicator3" class="loadingIndicator3" style="display: none"></div>
                    <div style="margin-left: auto; gap: 20px">
                        <input type="checkbox" id="checkbox1" name="option1">
                        <label for="checkbox1">使用云端保存的信息</label>
                        <select name="option" style="width: 200px;border:1px solid #ccc;background-color: #f5f5f5;">
                            {% for option in options %}
                            <option value="{{ option.id }}">{{ forloop.counter }}:{{ option.time }} - {{ option.info }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                </div>
                <div>修改后的文本：</div>

                <!-- <button id="cleanIN" class="cleanIN-button" >清除</button> -->
                <div>
                    <textarea id="displayText" placeholder="这里会显示文本..."></textarea>
                </div>
                <div>历史记录：
                    <select name="option" id="history"
                        style="width: 200px;border:1px solid #ccc;background-color: #f5f5f5;">
                    </select>
                </div>
                <button id="insert" class="btn btn-primary">插入</button>

            </div>

        </div>
    </div>
    <script src="{% static 'js/optimize.js' %}"></script>
</body>

</html>