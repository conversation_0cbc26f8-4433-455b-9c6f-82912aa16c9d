from langchain.schema import HumanMessage, SystemMessage
from langchain.prompts import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv(r"D:\硕士\aiagent\gpt_CWSC\.env")

# Ensure Google API Key is loaded
google_api_key = os.getenv("GOOGLE_API_KEY")
print("GOOGLE_API_KEY:", google_api_key)


if not google_api_key:
    raise ValueError("GOOGLE_API_KEY not found in environment variables")

from langchain_core.prompts import ChatPromptTemplate
#核心
llm = ChatGoogleGenerativeAI(
    model="gemini-2.5-pro",

    temperature=0,
    max_tokens=None,
    timeout=60,
    max_retries=2,
    api_key = google_api_key,
transport='rest' ,#非常关键
    # other params...
)
#参考
prompt = ChatPromptTemplate(#这里很关键，不要使用ChatPromptTemplate.from_messages
    [
        (
            "system",
            "You are a helpful assistant that translates {input_language} to {output_language}.",
        ),
        ("human", "{input}"),
    ]
)

chain = prompt | llm
res = chain.invoke(
    {
        "input_language": "English",
        "output_language": "German",
        "input": "I love programming.",
    }
)
print(res.content)