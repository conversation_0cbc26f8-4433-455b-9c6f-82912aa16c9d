from django.contrib.auth.models import User
from django.db import models
from django.db.models import UniqueConstraint

# Create your models here.
class Usage(models.Model):
    id = models.AutoField(primary_key=True)
    userid = models.ForeignKey(User, on_delete=models.CASCADE)
    use_time = models.DateField()
    prompt_token = models.IntegerField()
    output_token = models.IntegerField()
    use_model = models.CharField(max_length=20)
    class Meta:
        constraints = [
            UniqueConstraint(fields=['userid', 'use_time', 'use_model'], name='unique_userid_model_time')
        ]
        indexes = [
            models.Index(fields=['userid', 'use_time'], name='idx_userid_usetime')
        ]

