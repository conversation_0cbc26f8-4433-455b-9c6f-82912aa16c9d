# Generated by Django 5.0.1 on 2024-04-02 07:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Fileinfo',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('document_name', models.CharField(max_length=100)),
                ('size', models.CharField(max_length=100)),
                ('store_path', models.CharField(max_length=100)),
                ('create_time', models.DateTimeField()),
                ('upload_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.userinfo')),
            ],
        ),
    ]
