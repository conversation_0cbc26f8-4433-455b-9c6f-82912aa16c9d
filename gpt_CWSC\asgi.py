import os
import django
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gpt_CWSC.settings')
django.setup()
from gpt_CWSC import routing
application = ProtocolTypeRouter({
    'http': get_asgi_application(),
    # 使用 AuthMiddlewareStack 将 WebSocket 连接添加到连接范围
    'websocket': AuthMiddlewareStack(
        URLRouter(
            # 在这里添加你的 WebSocket 路由
            routing.websocket_urlpatterns
        )
    ),
})

