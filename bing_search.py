import os
import json
import requests
from requests.exceptions import Timeout
from bs4 import BeautifulSoup
from tqdm import tqdm
import time
import concurrent
from concurrent.futures import ThreadPoolExecutor
import pdfplumber
from io import BytesIO
import re
import string
from typing import Optional, Tuple
from nltk.tokenize import sent_tokenize

# ----------------------- Custom Headers -----------------------
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                  'AppleWebKit/537.36 (KHTML, like Gecko) '
                  'Chrome/58.0.3029.110 Safari/537.36',
    'Referer': 'https://www.google.com/',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1'
}

# Initialize session
session = requests.Session()
session.headers.update(headers)




def remove_punctuation(text: str) -> str:
    """去除文本中的标点符号。"""
    # 使用 str.maketrans 创建一个翻译表，指定要删除的字符
    return text.translate(str.maketrans("", "", string.punctuation))


def f1_score(true_set: set, pred_set: set) -> float:
    """计算两个单词集合之间的 F1 分数。

    参数:
        true_set (set): 真实的单词集合。
        pred_set (set): 预测的单词集合。

    返回:
        float: 两个集合之间的 F1 分数。
    """
    # 计算两个集合的交集大小
    intersection = len(true_set.intersection(pred_set))

    # 如果交集为空，直接返回 0.0
    if not intersection:
        return 0.0

    # 计算精确率 (Precision): 交集大小 / 预测集合大小
    precision = intersection / float(len(pred_set))

    # 计算召回率 (Recall): 交集大小 / 真实集合大小
    recall = intersection / float(len(true_set))

    # 计算 F1 分数: 2 * (精确率 * 召回率) / (精确率 + 召回率)
    return 2 * (precision * recall) / (precision + recall)


def extract_snippet_with_context(full_text: str, snippet: str, context_chars: int = 2500) -> Tuple[bool, str]:
    """
    从完整文本中提取与片段最匹配的句子及其上下文。

    参数:
        full_text (str): 从网页中提取的完整文本。
        snippet (str): 需要匹配的片段。
        context_chars (int): 在片段前后包含的字符数，默认为 2500。

    返回:
        Tuple[bool, str]: 第一个元素表示提取是否成功，第二个元素是提取的上下文。
    """
    try:
        # 限制完整文本的长度，避免处理过长的文本
        full_text = full_text[:50000]

        # 将片段转换为小写并移除标点符号
        snippet = snippet.lower()
        snippet = remove_punctuation(snippet)
        # 将片段拆分为单词集合
        snippet_words = set(snippet.split())

        # 初始化最佳句子和最佳 F1 分数
        best_sentence = None
        best_f1 = 0.2  # 设置一个最低阈值，只有 F1 分数高于此值才会被选中

        # 使用 nltk 的 sent_tokenize 将完整文本拆分为句子列表
        sentences = sent_tokenize(full_text)

        # 遍历每个句子，计算其与片段的 F1 分数
        for sentence in sentences:
            key_sentence = sentence.lower()
            key_sentence = remove_punctuation(key_sentence)
            sentence_words = set(key_sentence.split())
            # 计算当前句子与片段的 F1 分数
            f1 = f1_score(snippet_words, sentence_words)
            # 如果当前 F1 分数高于最佳分数，则更新最佳句子和最佳分数
            if f1 > best_f1:
                best_f1 = f1
                best_sentence = sentence

        # 如果找到最佳句子，提取其上下文
        if best_sentence:
            # 找到最佳句子在完整文本中的起始和结束位置
            para_start = full_text.find(best_sentence)
            para_end = para_start + len(best_sentence)
            # 计算上下文的起始和结束位置，确保不超出文本范围
            start_index = max(0, para_start - context_chars)
            end_index = min(len(full_text), para_end + context_chars)
            # 提取上下文
            context = full_text[start_index:end_index]
            return True, context
        else:
            # 如果未找到匹配的句子，返回完整文本的前 context_chars * 2 个字符
            return False, full_text[:context_chars * 2]
    except Exception as e:
        # 如果发生异常，返回错误信息
        return False, f"Failed to extract snippet context due to {str(e)}"



def extract_text_from_url(url, use_jina=False, jina_api_key=None, snippet: Optional[str] = None) -> str:
    """
    从 URL 中提取文本。如果提供了片段，则提取与之相关的上下文。

    参数:
        url (str): 网页或 PDF 的 URL。
        use_jina (bool): 是否使用 Jina 进行提取。
        jina_api_key (str): Jina API 的密钥。
        snippet (Optional[str]): 需要搜索的片段。

    返回:
        str: 提取的文本或上下文。
    """
    try:
        if use_jina:
            # 设置 Jina API 的请求头
            jina_headers = {
                'Authorization': f'Bearer {jina_api_key}',
                'X-Return-Format': 'markdown',
                # 'X-With-Links-Summary': 'true'
            }
            # 发送请求到 Jina API 并获取响应
            response = requests.get(f'https://r.jina.ai/{url}', headers=jina_headers).text
            # 使用正则表达式移除 URL
            pattern = r"\(https?:.*?\)|\[https?:.*?\]"
            text = re.sub(pattern, "", response).replace('---', '-').replace('===', '=').replace('   ', ' ').replace(
                '   ', ' ')
        else:
            # 使用 requests 库发送请求，设置超时时间为 20 秒
            response = requests.get(url, timeout=20)
            response.raise_for_status()  # 如果请求失败，抛出 HTTPError
            # 获取响应的内容类型
            content_type = response.headers.get('Content-Type', '')
            if 'pdf' in content_type:
                # 如果是 PDF 文件，调用 extract_pdf_text 函数提取文本
                return extract_pdf_text(url)
            # 尝试使用 lxml 解析器，如果失败则回退到 html.parser
            try:
                soup = BeautifulSoup(response.text, 'lxml')
            except Exception:
                print("lxml parser not found or failed, falling back to html.parser")
                soup = BeautifulSoup(response.text, 'html.parser')
            # 提取网页的纯文本内容
            text = soup.get_text(separator=' ', strip=True)

        if snippet:
            # 如果提供了片段，调用 extract_snippet_with_context 函数提取上下文
            success, context = extract_snippet_with_context(text, snippet)
            if success:
                return context
            else:
                return text
        else:
            # 如果未提供片段，直接返回前 8000 个字符的文本
            return text[:8000]
    except requests.exceptions.HTTPError as http_err:
        # 捕获 HTTP 错误并返回错误信息
        return f"HTTP error occurred: {http_err}"
    except requests.exceptions.ConnectionError:
        # 捕获连接错误并返回错误信息
        return "Error: Connection error occurred"
    except requests.exceptions.Timeout:
        # 捕获超时错误并返回错误信息
        return "Error: Request timed out after 20 seconds"
    except Exception as e:
        # 捕获其他异常并返回错误信息
        return f"Unexpected error: {str(e)}"



def fetch_page_content(urls: list, max_workers: int = 4, use_jina: bool = False, snippets: Optional[dict] = None) -> dict:
    """
    并发地从多个 URL 获取内容。

    参数:
        urls (list): 要抓取的 URL 列表。
        max_workers (int): 最大并发线程数。
        use_jina (bool): 是否使用 Jina 进行提取。
        snippets (Optional[dict]): 一个字典，将 URL 映射到其相应的片段。

    返回:
        dict: 一个字典，将 URL 映射到提取的内容或上下文。
    """
    results = {}
    # 使用 ThreadPoolExecutor 创建一个线程池
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 使用 tqdm 显示进度条
        futures = {
            executor.submit(extract_text_from_url, url, use_jina, snippets.get(url) if snippets else None): url
            for url in urls
        }
        # 遍历已完成的任务
        for future in tqdm(concurrent.futures.as_completed(futures), desc="Fetching URLs", total=len(urls)):
            url = futures[future]  # 获取对应的 URL
            try:
                data = future.result()  # 获取任务结果
                results[url] = data  # 将结果存入字典
            except Exception as exc:
                results[url] = f"获取 {url} 时出错: {exc}"  # 处理异常并记录错误信息
            time.sleep(0.2)  # 简单的速率限制
    return results


def bing_web_search(query, subscription_key, endpoint, market='en-US', language='en', timeout=20):
    """
    Perform a search using the Bing Web Search API with a set timeout.

    Args:
        query (str): Search query.
        subscription_key (str): Subscription key for the Bing Search API.
        endpoint (str): Endpoint for the Bing Search API.
        market (str): Market, e.g., "en-US" or "zh-CN".
        language (str): Language of the results, e.g., "en".
        timeout (int or float or tuple): Request timeout in seconds.
                                         Can be a float representing the total timeout,
                                         or a tuple (connect timeout, read timeout).

    Returns:
        dict: JSON response of the search results. Returns None or raises an exception if the request times out.
    """
    headers = {
        "Ocp-Apim-Subscription-Key": subscription_key
    }
    params = {
        "q": query,
        "mkt": market,
        "setLang": language,
        "textDecorations": True,
        "textFormat": "HTML"
    }

    try:
        response = requests.get(endpoint, headers=headers, params=params, timeout=timeout)
        response.raise_for_status()  # Raise exception if the request failed
        search_results = response.json()
        return search_results
    except Timeout:
        print(f"Bing Web Search request timed out ({timeout} seconds) for query: {query}")
        return {}  # Or you can choose to raise an exception
    except requests.exceptions.RequestException as e:
        print(f"Error occurred during Bing Web Search request: {e}")
        return {}


def extract_pdf_text(url):
    """
    Extract text from a PDF.

    Args:
        url (str): URL of the PDF file.

    Returns:
        str: Extracted text content or error message.
    """
    try:
        response = session.get(url, timeout=20)  # Set timeout to 20 seconds
        if response.status_code != 200:
            return f"Error: Unable to retrieve the PDF (status code {response.status_code})"

        # Open the PDF file using pdfplumber
        with pdfplumber.open(BytesIO(response.content)) as pdf:
            full_text = ""
            for page in pdf.pages:
                text = page.extract_text()
                if text:
                    full_text += text

        # Limit the text length
        cleaned_text = ' '.join(full_text.split()[:600])
        return cleaned_text
    except requests.exceptions.Timeout:
        return "Error: Request timed out after 20 seconds"
    except Exception as e:
        return f"Error: {str(e)}"


def extract_relevant_info(search_results):
    """
    Extract relevant information from Bing search results.

    Args:
        search_results (dict): JSON response from the Bing Web Search API.

    Returns:
        list: A list of dictionaries containing the extracted information.
    """
    useful_info = []

    if 'webPages' in search_results and 'value' in search_results['webPages']:
        for id, result in enumerate(search_results['webPages']['value']):
            info = {
                'id': id + 1,  # Increment id for easier subsequent operations
                'title': result.get('name', ''),
                'url': result.get('url', ''),
                'site_name': result.get('siteName', ''),
                'date': result.get('datePublished', '').split('T')[0],
                'snippet': result.get('snippet', ''),  # Remove HTML tags
                # Add context content to the information
                'context': ''  # Reserved field to be filled later
            }
            useful_info.append(info)

    return useful_info


# ------------------------------------------------------------

if __name__ == "__main__":
    # Example usage
    # Define the query to search
    query = "Structure of dimethyl fumarate"

    # Subscription key and endpoint for Bing Search API
    BING_SUBSCRIPTION_KEY = "YOUR_BING_SUBSCRIPTION_KEY"
    if not BING_SUBSCRIPTION_KEY:
        raise ValueError("Please set the BING_SEARCH_V7_SUBSCRIPTION_KEY environment variable.")

    bing_endpoint = "https://api.bing.microsoft.com/v7.0/search"

    # Perform the search
    print("Performing Bing Web Search...")
    search_results = bing_web_search(query, BING_SUBSCRIPTION_KEY, bing_endpoint)

    print("Extracting relevant information from search results...")
    extracted_info = extract_relevant_info(search_results)

    print("Fetching and extracting context for each snippet...")
    for info in tqdm(extracted_info, desc="Processing Snippets"):
        full_text = extract_text_from_url(info['url'], use_jina=True)  # Get full webpage text
        if full_text and not full_text.startswith("Error"):
            success, context = extract_snippet_with_context(full_text, info['snippet'])
            if success:
                info['context'] = context
            else:
                info['context'] = f"Could not extract context. Returning first 8000 chars: {full_text[:8000]}"
        else:
            info['context'] = f"Failed to fetch full text: {full_text}"

    # print("Your Search Query:", query)
    # print("Final extracted information with context:")
    # print(json.dumps(extracted_info, indent=2, ensure_ascii=False))
