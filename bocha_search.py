import os

import requests
from dotenv import load_dotenv
from langchain.agents import initialize_agent, <PERSON><PERSON>, AgentType
from langchain_openai import ChatOpenAI
from langchain.tools import tool

OPENAI_API_KEY = "YOUR-OPENAI-API-KEY"
BOCHA_API_KEY = "sk-683311eac0ab4897be2c1f1d6295a4ad"


# 定义Bocha Web Search工具
#@tool
def bocha_websearch_tool(query: str, count: int = 10) -> str:
    """
  使用Bocha Web Search API 进行网页搜索。

  参数:
  - query: 搜索关键词
  - freshness: 搜索的时间范围
  - summary: 是否显示文本摘要
  - count: 返回的搜索结果数量

  返回:
  - 搜索结果的详细信息，包括网页标题、网页URL、网页摘要、网站名称、网站Icon、网页发布时间等。
  """

    url = 'https://api.bochaai.com/v1/web-search'
    headers = {
        'Authorization': f'Bearer {BOCHA_API_KEY}',  # 请替换为你的API密钥
        'Content-Type': 'application/json'
    }
    data = {
        "query": query,
        "freshness": "noLimit",  # 搜索的时间范围，例如 "oneDay", "oneWeek", "oneMonth", "oneYear", "noLimit"
        "summary": True,  # 是否返回长文本摘要
        "count": count
    }

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        json_response = response.json()
        try:
            if json_response["code"] != 200 or not json_response["data"]:
                return f"搜索API请求失败，原因是: {response.msg or '未知错误'}"

            webpages = json_response["data"]["webPages"]["value"]
            if not webpages:
                return "未找到相关结果。"
            formatted_results = ""
            for idx, page in enumerate(webpages, start=1):
                formatted_results += (
                    f"引用: {idx}\n"
                    f"标题: {page['name']}\n"
                    f"URL: {page['url']}\n"
                    f"摘要: {page['summary']}\n"
                    f"网站名称: {page['siteName']}\n"
                    f"网站图标: {page['siteIcon']}\n"
                    f"发布时间: {page['dateLastCrawled']}\n\n"
                )
            return formatted_results.strip()
        except Exception as e:
            return f"搜索API请求失败，原因是：搜索结果解析失败 {str(e)}"
    else:
        return f"搜索API请求失败，状态码: {response.status_code}, 错误信息: {response.text}"

res = bocha_websearch_tool("阿里巴巴2024年ESG报告亮点")
print(res)


# # 创建LangChain工具
# bocha_tool = Tool(
#     name="BochaWebSearch",
#     func=bocha_websearch_tool,
#     description="使用Bocha Web Search API 进行搜索互联网网页，输入应为搜索查询字符串，输出将返回搜索结果的详细信息，包括网页标题、网页URL、网页摘要、网站名称、网站Icon、网页发布时间等。"
# )
#
# load_dotenv()
# deepseek_api_key = os.getenv("DEEPSEEK_API_KEY2")
#
#
#
# ##第二种deepseek的调用方式
# llm = ChatOpenAI(
#     model='deepseek-chat',
#     openai_api_key=deepseek_api_key,
#     openai_api_base='https://api.deepseek.com/v1',
#     max_tokens=4096,
#     temperature=1.5
# )
# # 初始化代理，包含您的自定义工具
# agent = initialize_agent(
#     common_tools=[bocha_tool],
#     llm=llm,
#     agent=AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION,
#     verbose=True
# )
#
# # 使用代理进行查询
# user_question = "请告诉我阿里巴巴2024年ESG报告中的亮点"
# response = agent.invoke(user_question)
# print(response)