import io
import os

import fitz
import pytesseract
from docx import Document
from pptx import Presentation
from PIL import Image


def Get_text(file):
    stream = io.BytesIO(file.read())  # 创建一个二进制流
    _, ext = os.path.splitext(file.name)
    text = ""

    if ext == ".pdf":
        doc = fitz.open("pdf", stream)
        for page in doc:
            text += page.get_text()
        doc.close()
    elif ext == ".doc":
        doc = Document(stream)
        for para in doc.paragraphs:
            text += para.text + "\n"
    elif ext in [".ppt", ".pptx"]:
        pres = Presentation(stream)
        for slide in pres.slides:
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    text += shape.text + "\n"
    else:
        raise ValueError(f"Unsupported file extension: {ext}")
    return text


def Get_text_from_pic(file):
    """Extract text from a file (PDF, DOC, PPT, PPTX) using PyMuPDF, python-docx, and python-pptx."""
    stream = io.BytesIO(file.read())  # 创建一个二进制流
    _, ext = os.path.splitext(file.name)
    text = ""

    if ext == ".pdf":
        doc = fitz.open("pdf", stream)
        for page_number in range(len(doc)):
            page = doc.load_page(page_number)  # 加载 PDF 页面
            pix = page.get_pixmap(matrix=fitz.Matrix(1 / 2, 1 / 2))  # 渲染页面为图像
            img_bytes = pix.tobytes("ppm")  # 将图像转换为 PPM 格式的字节数据
            img = Image.open(io.BytesIO(img_bytes))  # 将 PPM 字节数据转换为 PIL 图像对象
            text += pytesseract.image_to_string(img, lang='chi_sim+eng')
        doc.close()
    elif ext == ".doc":
        doc = Document(stream)
        for para in doc.paragraphs:
            text += para.text + "\n"
    elif ext in [".ppt", ".pptx"]:
        pres = Presentation(stream)
        for slide in pres.slides:
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    text += shape.text + "\n"
                if hasattr(shape, "image"):
                    img = shape.image
                    img_bytes = img.blob
                    img = Image.open(io.BytesIO(img_bytes))
                    text += pytesseract.image_to_string(img, lang='chi_sim+eng')
    else:
        raise ValueError(f"Unsupported file extension: {ext}")

    return text
