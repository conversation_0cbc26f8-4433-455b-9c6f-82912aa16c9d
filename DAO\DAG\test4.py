import itertools
import math


def get_coalitions(players):
    """Generates all non-empty coalitions for a set of players."""
    #  对于给定的玩家集合，生成所有非空联盟。
    return [frozenset(c) for i in range(1, len(players) + 1) for c in itertools.combinations(players, i)]


def find_connected_components(coalition, graph):
    """Finds connected components within a coalition given a graph."""
    #  给定一个图，找出联盟内的连接组件。
    players_in_coalition = set(coalition)
    unvisited = set(players_in_coalition)
    components = []

    while unvisited:
        start_node = next(iter(unvisited))
        component = set()
        queue = [start_node]
        visited_in_component = set()

        while queue:
            player = queue.pop(0)
            if player not in visited_in_component:
                visited_in_component.add(player)
                component.add(player)
                unvisited.discard(player)

                # Find neighbors in the graph that are also in the coalition
                #  找到图中也是联盟成员的邻居
                neighbors = [
                                m for n, m in graph if n == player and m in players_in_coalition
                            ] + [
                                n for n, m in graph if m == player and n in players_in_coalition
                            ]
                for neighbor in neighbors:
                    if neighbor not in visited_in_component:
                        queue.append(neighbor)

        if component:
            components.append(frozenset(component))

    return components


def calculate_modified_game_value(original_game, coalition, graph):
    """Calculates the value of a coalition in the modified game (v/g)."""
    #  计算修改后的博弈 (v/g) 中联盟的值。
    connected_components = find_connected_components(coalition, graph)
    modified_value = 0
    for component in connected_components:
        # Ensure component exists as a key in the original game's characteristic function
        # 确保组件作为原始博弈的特征函数中的键存在
        if component in original_game:
            modified_value += original_game[component]
        # Handle cases where a component might not have a defined value in the input game
        # (e.g., if input game only specifies values for single players and grand coalition)
        # For this implementation, we assume values are defined for all relevant subsets
        # based on the paper's context of characteristic function games.
        #  处理输入博弈中组件可能没有定义值的情况
        #  （例如，如果输入博弈仅指定单个玩家和总联盟的值）
        #  对于此实现，我们假设基于论文的特征函数博弈的上下文，为所有相关子集定义了值。
        elif frozenset() not in original_game:  # Check if empty set is handled, although not typical for CFG
            pass  # Or handle error/warning if a component value is missing
        else:
            # If the empty set has a value defined, it might be relevant
            #  如果为空集定义了值，它可能是相关的
            if frozenset() in original_game:
                modified_value += original_game[frozenset()]

    return modified_value


def calculate_shapley_value(game_values, players):
    """Calculates the Shapley value for each player in a game."""
    #  计算博弈中每个玩家的 Shapley 值。
    n = len(players)
    shapley_values = {player: 0 for player in players}
    all_coalitions = get_coalitions(players)

    for player in players:
        for coalition in all_coalitions:
            if player in coalition:
                coalition_without_player = frozenset(coalition - {player})
                # Ensure coalition_without_player is a valid key or handle empty set
                # 确保 coalition_without_player 是一个有效的键或处理空集
                val_coalition = game_values.get(coalition, 0)  # Default to 0 if coalition not in game_values
                val_coalition_without = game_values.get(coalition_without_player,
                                                        0)  # Default to 0 for empty set or missing key

                marginal_contribution = val_coalition - val_coalition_without

                s = len(coalition)
                # The original Shapley formula uses (s-1)! * (n-s)! / n!
                # s is the size of the coalition *with* the player
                # n-s is the number of players outside the coalition *with* the player
                #  原始的 Shapley 公式使用 (s-1)! * (n-s)! / n!
                #  s 是包含玩家的联盟的大小
                #  n-s 是不包含玩家的联盟之外的玩家数量
                numerator = math.factorial(s - 1) * math.factorial(n - s)
                denominator = math.factorial(n)
                weight = numerator / denominator

                shapley_values[player] += weight * marginal_contribution
    return shapley_values


def calculate_fair_allocation(players, original_game_values, graph):
    """
    Calculates the fair allocation rule (Shapley value of the modified game)
    for a characteristic function game and a graph.
    """
    #  计算特征函数博弈和图的公平分配规则（修改后博弈的 Shapley 值）。

    # 1. Define the modified game (v/g)
    #  1. 定义修改后的博弈 (v/g)
    modified_game_values = {}
    all_coalitions = get_coalitions(players)
    for coalition in all_coalitions:
        modified_game_values[coalition] = calculate_modified_game_value(original_game_values, coalition, graph)
    # The empty set is not a coalition in the standard definition, but for Shapley calculation
    # we might need the value of coalition - {player} where coalition is a singleton.
    # The value of the empty set is typically 0 in characteristic function games.
    #  空集不是标准定义中的联盟，但对于 Shapley 计算
    #  我们可能需要 coalition - {player} 的值，其中 coalition 是一个单例。
    #  在特征函数博弈中，空集的值通常为 0。
    modified_game_values[frozenset()] = 0

    # 2. Calculate the Shapley value of the modified game
    #  2. 计算修改后博弈的 Shapley 值
    fair_allocation = calculate_shapley_value(modified_game_values, players)

    return fair_allocation


def parse_coalition_value(coalition_str, value):
    """将字符串格式的联盟转换为frozenset"""
    # 将例如 ('a','b','c') 解析为 frozenset({'a', 'b', 'c'})
    # 去除括号和空格，然后分割
    if coalition_str == '()':
        return frozenset(), value
    coalition_str = coalition_str.strip("()").replace("'", "").replace('"', '')
    players = [p.strip() for p in coalition_str.split(',')]
    return frozenset(players), value


def parse_graph_edges(edges_str):
    """解析图的边缘结构"""
    # 去除最外层的方括号
    edges_str = edges_str.strip("[]")
    # 分割为单独的边
    edges = []
    if not edges_str:
        return set()

    # 使用括号匹配来解析元组
    start = 0
    level = 0
    for i, char in enumerate(edges_str):
        if char == '(' and (i == 0 or edges_str[i - 1] != "'"):
            if level == 0:
                start = i
            level += 1
        elif char == ')' and (i == len(edges_str) - 1 or edges_str[i + 1] != "'"):
            level -= 1
            if level == 0:
                edge_str = edges_str[start:i + 1]
                # 解析单个边
                edge_str = edge_str.strip("()").replace("'", "").replace('"', '')
                edge_parts = [p.strip() for p in edge_str.split(',')]
                if len(edge_parts) >= 2:
                    edges.append((edge_parts[0], edge_parts[1]))
                # 寻找下一个边的起始位置
                while i + 1 < len(edges_str) and edges_str[i + 1] not in ['(', ',']:
                    i += 1

    return set(edges)


def parse_coalition_values(values_str):
    """解析联盟值字典"""
    # 去除最外层的花括号
    values_str = values_str.strip("{}")
    if not values_str:
        return {}

    result = {}
    # 找到所有键值对
    entries = []
    start = 0
    level = 0
    i = 0

    while i < len(values_str):
        char = values_str[i]
        # 处理键部分
        if char == '(' and (i == 0 or values_str[i - 1] != "'"):
            if level == 0:
                start = i
            level += 1
        elif char == ')' and (i == len(values_str) - 1 or values_str[i + 1] != "'"):
            level -= 1
            if level == 0:
                # 寻找冒号
                j = i + 1
                while j < len(values_str) and values_str[j] != ':':
                    j += 1

                if j < len(values_str):
                    # 寻找值的结束位置
                    k = j + 1
                    while k < len(values_str) and values_str[k] not in [',']:
                        k += 1

                    key_str = values_str[start:i + 1]
                    value_str = values_str[j + 1:k].strip()

                    try:
                        value = int(value_str)
                        coalition, _ = parse_coalition_value(key_str, 0)
                        result[coalition] = value
                    except ValueError:
                        # 处理值不是整数的情况
                        pass

                    i = k
                    if i < len(values_str) and values_str[i] == ',':
                        i += 1
                    continue

        i += 1

    # 添加空集的值
    result[frozenset()] = 0

    return result


def calculate_traditional_shapley(original_game_values, players):
    """计算传统的Shapley值（不考虑图的连通性）"""
    n = len(players)
    shapley_values = {player: 0 for player in players}

    for player in players:
        # 获取除当前玩家外的所有玩家列表
        other_players = [p for p in players if p != player]

        # 遍历所有可能的联盟大小
        for subset_size in range(n):
            # 计算权重
            weight = math.factorial(subset_size) * math.factorial(n - subset_size - 1) / math.factorial(n)

            # 遍历所有不包含该玩家的子集
            for subset in itertools.combinations(other_players, subset_size):
                # 创建不含当前玩家的联盟
                coalition_without_player = frozenset(subset)

                # 创建包含当前玩家的联盟
                coalition_with_player = frozenset(subset) | {player}

                # 获取联盟价值
                value_without = original_game_values.get(coalition_without_player, 0)
                value_with = original_game_values.get(coalition_with_player, 0)

                # 计算边际贡献
                marginal_contribution = value_with - value_without

                # 更新Shapley值
                shapley_values[player] += weight * marginal_contribution

    return shapley_values


def get_connected_coalitions(players, graph):
    """
    根据图结构找出所有连通的联盟

    参数:
    - players: 所有玩家列表
    - graph: 图结构，表示为边的集合，如 {('a','b'), ('b','c'), ...}

    返回:
    - 所有连通联盟的列表
    """
    all_coalitions = get_coalitions(players)
    connected_coalitions = []

    for coalition in all_coalitions:
        # 对每个联盟，检查它是否是连通的
        components = find_connected_components(coalition, graph)
        # 如果联盟只有一个连通分量，并且该分量包含了联盟中的所有玩家，则该联盟是连通的
        if len(components) == 1 and len(components[0]) == len(coalition):
            connected_coalitions.append(coalition)

    # 添加空集
    connected_coalitions.append(frozenset())

    return connected_coalitions


def calculate_optimized_fair_allocation(players, original_game_values, graph):
    """
    优化的公平分配规则计算 - 考虑图结构处理联盟

    这个函数计算基于图结构的联盟值，对于非连通联盟，根据连通分量计算值。

    参数:
    - players: 玩家列表
    - original_game_values: 原始博弈中所有联盟的价值，以 frozenset 为键
    - graph: 图结构，表示为边的集合，如 {('a','b'), ('b','c'), ...}

    返回:
    - 修改后博弈的Shapley值，即公平分配值
    """
    # 1. 找出连通联盟
    connected_coalitions = get_connected_coalitions(players, graph)
    all_coalitions = get_coalitions(players)
    print(f"需要计算的连通联盟数量: {len(connected_coalitions)}，而不是全部 {2 ** len(players)} 个联盟")

    # 2. 为所有联盟计算修改后博弈值
    modified_game_values = {}

    # 先处理空集
    modified_game_values[frozenset()] = 0

    # 处理所有非空联盟
    for coalition in all_coalitions:
        if len(coalition) == 1:  # 单一玩家联盟
            modified_game_values[coalition] = original_game_values.get(coalition, 0)
        else:
            # 判断联盟是否连通
            components = find_connected_components(coalition, graph)
            if len(components) == 1:  # 连通联盟
                modified_game_values[coalition] = original_game_values.get(coalition, 0)
            else:  # 非连通联盟
                # 对于非连通联盟，累加其连通分量的值
                value = 0
                for component in components:
                    value += original_game_values.get(component, 0)
                modified_game_values[coalition] = value

    # 3. 计算修改后博弈的Shapley值
    fair_allocation = calculate_shapley_value(modified_game_values, players)

    return fair_allocation


def main():
    # 用户输入玩家列表
    players_input = input("请输入玩家列表（用逗号分隔，例如 a,b,c,d,e,f）: ")
    players = [p.strip() for p in players_input.split(',')]

    # 用户输入图的结构
    print("请输入图的结构（例如 [('a','c'), ('a','d'), ('b','c')]），或直接回车使用默认图结构: ")
    graph_input = input()
    if not graph_input:
        # 使用默认图结构的例子
        graph = {('a', 'c'), ('a', 'd'), ('a', 'e'), ('b', 'c'), ('b', 'd'), ('b', 'e'),
                 ('c', 'd'), ('c', 'e'), ('d', 'e'), ('c', 'f'), ('d', 'f'), ('e', 'f')}
    else:
        graph = parse_graph_edges(graph_input)

    # 用户输入联盟值
    print("请输入联盟值（以字典格式，例如 {('a',): 5, ('a','b'): 15}），或直接回车使用默认联盟值: ")
    values_input = input()
    if not values_input:
        # 使用默认联盟值的例子
        original_game_values = {
            frozenset({'a'}): 5, frozenset({'b'}): 7, frozenset({'c'}): 3,
            frozenset({'d'}): 4, frozenset({'e'}): 6, frozenset({'f'}): 2,
            frozenset({'a', 'b'}): 15, frozenset({'b', 'c'}): 12,
            frozenset({'c', 'd'}): 9, frozenset({'d', 'e'}): 13,
            frozenset({'e', 'f'}): 10, frozenset({'a', 'c'}): 11,
            frozenset({'a', 'd'}): 14, frozenset({'b', 'd'}): 16,
            frozenset({'b', 'e'}): 8, frozenset({'c', 'e'}): 17,
            frozenset({'c', 'f'}): 7, frozenset({'d', 'f'}): 11,
            frozenset({'a', 'f'}): 9, frozenset({'b', 'f'}): 13,
            frozenset({'a', 'e'}): 12, frozenset({'a', 'b', 'c'}): 23,
            frozenset({'b', 'c', 'd'}): 20, frozenset({'c', 'd', 'e'}): 25,
            frozenset({'d', 'e', 'f'}): 19, frozenset({'a', 'b', 'd'}): 24,
            frozenset({'a', 'c', 'e'}): 22, frozenset({'b', 'd', 'f'}): 21,
            frozenset({'a', 'e', 'f'}): 18, frozenset({'b', 'c', 'f'}): 15,
            frozenset({'a', 'c', 'd'}): 17, frozenset({'b', 'e', 'f'}): 22,
            frozenset({'a', 'd', 'e'}): 23, frozenset({'a', 'b', 'f'}): 20,
            frozenset({'c', 'e', 'f'}): 16, frozenset({'a', 'b', 'c', 'd'}): 30,
            frozenset({'b', 'c', 'd', 'e'}): 32, frozenset({'c', 'd', 'e', 'f'}): 28,
            frozenset({'a', 'b', 'c', 'e'}): 33, frozenset({'a', 'b', 'd', 'e'}): 35,
            frozenset({'a', 'c', 'd', 'e'}): 31, frozenset({'b', 'c', 'd', 'f'}): 27,
            frozenset({'a', 'b', 'e', 'f'}): 29, frozenset({'a', 'c', 'd', 'f'}): 26,
            frozenset({'a', 'd', 'e', 'f'}): 34, frozenset({'b', 'c', 'e', 'f'}): 31,
            frozenset({'b', 'd', 'e', 'f'}): 30, frozenset({'a', 'b', 'c', 'd', 'e'}): 42,
            frozenset({'b', 'c', 'd', 'e', 'f'}): 40, frozenset({'a', 'c', 'd', 'e', 'f'}): 38,
            frozenset({'a', 'b', 'd', 'e', 'f'}): 41, frozenset({'a', 'b', 'c', 'e', 'f'}): 39,
            frozenset({'a', 'b', 'c', 'd', 'f'}): 37, frozenset({'a', 'b', 'c', 'd', 'e', 'f'}): 50
        }
        original_game_values[frozenset()] = 0
    else:
        original_game_values = parse_coalition_values(values_input)

    # 计算修改后博弈的公平分配（考虑图结构的Shapley值）
    modified_fair_allocation = calculate_optimized_fair_allocation(players, original_game_values, graph)

    # 计算传统的Shapley值（不考虑图结构）
    traditional_shapley_values = calculate_traditional_shapley(original_game_values, players)

    # 计算大联盟价值和Shapley值总和
    grand_coalition = frozenset(players)
    grand_coalition_value = original_game_values.get(grand_coalition, 0)
    shapley_total = sum(traditional_shapley_values.values())
    modified_shapley_total = sum(modified_fair_allocation.values())

    print(f"\n玩家: {players}")
    print(f"图结构: {graph}")
    print(f"大联盟价值: {grand_coalition_value}")
    print(f"传统Shapley值总和: {shapley_total:.4f}")
    print(f"修改后博弈的Shapley值总和: {modified_shapley_total:.4f}")

    print("\n考虑图结构的Shapley值(修改后博弈的Shapley值):")
    for player, value in modified_fair_allocation.items():
        # 添加除零检查
        if modified_shapley_total != 0:
            percentage = (value / modified_shapley_total) * 100
            print(f"玩家 {player}: {value:.4f} ({percentage:.2f}%)")
        else:
            print(f"玩家 {player}: {value:.4f} (无法计算百分比，总和为零)")

    print("\n传统的Shapley值(不考虑图结构):")
    for player, value in traditional_shapley_values.items():
        # 添加除零检查
        if shapley_total != 0:
            percentage = (value / shapley_total) * 100
            print(f"玩家 {player}: {value:.4f} ({percentage:.2f}%)")
        else:
            print(f"玩家 {player}: {value:.4f} (无法计算百分比，总和为零)")


if __name__ == "__main__":
    main()