from dotenv import load_dotenv
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
import os

# 加载环境变量
load_dotenv()
deepseek_api_key = '***********************************'

# 初始化DeepSeek模型
llm = ChatOpenAI(
    model='deepseek-chat',
    openai_api_key=deepseek_api_key,
    openai_api_base='https://api.deepseek.com/v1',
    max_tokens=4096,
    temperature=0.7
)

# 定义聊天模板
prompt = ChatPromptTemplate(
    messages=[
        ("system", "你是一个专业的{role}助手，擅长{specialty}。"),
        ("human", "{question}")
    ]
)

# 创建链
chain = prompt | llm


# 测试调用
def chat_with_assistant(role, specialty, question):
    response = chain.invoke({
        "role": role,
        "specialty": specialty,
        "question": question
    })
    return response.content


def main():
    # 示例调用
    role = "程序员"
    specialty = "Python编程"
    question = "请解释一下Python中装饰器的概念和用法"

    print("正在询问AI助手...")
    response = chat_with_assistant(role, specialty, question)
    print("\n助手回答:")
    print(response)


if __name__ == "__main__":
    main()