import os
import time

import fitz  # PyMuPDF
import io

import pytesseract
from PIL import Image
from docx import Document
from pptx import Presentation


def get_text(file):
    """Extract text from a file (PDF, DOC, PPT, PPTX) using PyMuPDF, python-docx, and python-pptx."""
    stream = io.BytesIO(file.read())  # 创建一个二进制流
    _, ext = os.path.splitext(file.name)
    text = ""

    start = time.time()

    if ext == ".pdf":
        doc = fitz.open("pdf", stream)
        for page_number in range(len(doc)):
            page = doc.load_page(page_number)  # 加载 PDF 页面
            pix = page.get_pixmap(matrix=fitz.Matrix(1 / 2, 1 / 2))  # 渲染页面为图像
            img_bytes = pix.tobytes("ppm")  # 将图像转换为 PPM 格式的字节数据
            img = Image.open(io.BytesIO(img_bytes))  # 将 PPM 字节数据转换为 PIL 图像对象
            text += pytesseract.image_to_string(img, lang='chi_sim+eng')
        doc.close()
    elif ext == ".doc":
        doc = Document(stream)
        for para in doc.paragraphs:
            text += para.text + "\n"
    elif ext in [".ppt", ".pptx"]:
        pres = Presentation(stream)
        for slide in pres.slides:
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    text += shape.text + "\n"
                if hasattr(shape, "image"):
                    img = shape.image
                    img_bytes = img.blob
                    img = Image.open(io.BytesIO(img_bytes))
                    text += pytesseract.image_to_string(img, lang='chi_sim+eng')
    else:
        raise ValueError(f"Unsupported file extension: {ext}")

    elapsed_time = time.time() - start
    print(f"Time taken for conversion: {elapsed_time} seconds")
    return text

if __name__ == "__main__":
    pdf_path = '2. 妇女节女装合集文案brief.pptx'  # 替换为你的 PDF 文件路径

