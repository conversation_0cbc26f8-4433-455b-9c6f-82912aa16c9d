from dotenv import load_dotenv
from langchain.schema import SystemMessage, HumanMessage, StrOutputParser

from langchain.prompts import ChatPromptTemplate, PromptTemplate
import os
from langchain_openai import ChatOpenAI

load_dotenv()
deepseek_api_key = "***********************************"

# llm = ChatDeepSeek(
#     model="deepseek-ai/DeepSeek-V3",
#     temperature=0,
#     max_tokens=None,
#     timeout=40,
#     max_retries=2,
#     api_base="https://api.siliconflow.cn",
#     api_key=deepseek_api_key,
# )

##第二种deepseek的调用方式
llm = ChatOpenAI(
    model='deepseek-chat',
    openai_api_key=deepseek_api_key,
    openai_api_base='https://api.deepseek.com/v1',
    max_tokens=4096,
    temperature=0,
    top_p=1.0,
)


# 2、定义提示模版
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate(
    [
        (
            "system",
            "You are a helpful assistant that translates {input_language} to {output_language}.",
        ),
        ("human", "{input}"),
    ]
)

chain = prompt | llm
res = chain.invoke(
        {
            "input_language": "English",
            "output_language": "German",
            "input": "I love programming.",
        }
)
print(res.content)



