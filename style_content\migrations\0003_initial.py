# Generated by Django 5.0.1 on 2024-05-30 15:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('style_content', '0002_delete_usage'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CreatedContent',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('create_time', models.DateTimeField()),
                ('product_information', models.TextField()),
                ('Outline', models.TextField()),
                ('Final_output', models.TextField()),
                ('userid', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        # 添加 SQL 操作以更改字符集
        migrations.RunSQL(
            "ALTER TABLE style_content_createdcontent CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;",
            reverse_sql="ALTER TABLE style_content_createdcontent CONVERT TO CHARACTER SET utf8 COLLATE utf8_general_ci;"
        ),
    ]
