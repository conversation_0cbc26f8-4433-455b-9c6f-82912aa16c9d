import json

from dotenv import load_dotenv
from langchain.schema import SystemMessage, HumanMessage, StrOutputParser

from langchain.prompts import ChatPromptTemplate, PromptTemplate
import os
from langchain_openai import ChatOpenAI
from sqlalchemy import null

load_dotenv()
deepseek_api_key = "***********************************"
EXTRACT_CLAIMS_PROMPT = """
 -Target activity-
 You are an intelligent assistant that helps a human analyst to analyze claims against certain entities presented in a text document.

 -Goal-
 Given a text document that is potentially relevant to this activity, an entity specification, and a claim description, extract all entities that match the entity specification and all claims against those entities.

 -Steps-
 1. Extract all named entities that match the predefined entity specification. Entity specification can either be a list of entity names or a list of entity types.
 2. For each entity identified in step 1, extract all claims associated with the entity. Claims need to match the specified claim description, and the entity should be the subject of the claim.
 For each claim, extract the following information:
 - Subject: name of the entity that is subject of the claim, capitalized. The subject entity is one that committed the action described in the claim. Subject needs to be one of the named entities identified in step 1.
 - Object: name of the entity that is object of the claim, capitalized. The object entity is one that either reports/handles or is affected by the action described in the claim. If object entity is unknown, use **NONE**.
 - Claim Type: overall category of the claim, capitalized. Name it in a way that can be repeated across multiple text inputs, so that similar claims share the same claim type
 - Claim Status: **TRUE**, **FALSE**, or **SUSPECTED**. TRUE means the claim is confirmed, FALSE means the claim is found to be False, SUSPECTED means the claim is not verified.
 - Claim Description: Detailed description explaining the reasoning behind the claim, together with all the related evidence and references.
 - Claim Date: Period (start_date, end_date) when the claim was made. Both start_date and end_date should be in ISO-8601 format. If the claim was made on a single date rather than a date range, set the same date for both start_date and end_date. If date is unknown, return **NONE**.
 - Claim Source Text: List of **all** quotes from the original text that are relevant to the claim.

 Format each claim as (<subject_entity>{tuple_delimiter}<object_entity>{tuple_delimiter}<claim_type>{tuple_delimiter}<claim_status>{tuple_delimiter}<claim_start_date>{tuple_delimiter}<claim_end_date>{tuple_delimiter}<claim_description>{tuple_delimiter}<claim_source>)

 3. Return output in English as a single list of all the claims identified in steps 1 and 2. Use **{record_delimiter}** as the list delimiter.

 4. When finished, output {completion_delimiter}

 -Examples-
 Example 1:
 Entity specification: organization
 Claim description: red flags associated with an entity
 Text: According to an article on 2022/01/10, Company A was fined for bid rigging while participating in multiple public tenders published by Government Agency B. The company is owned by Person C who was suspected of engaging in corruption activities in 2015.
 Output:

 (COMPANY A{tuple_delimiter}GOVERNMENT AGENCY B{tuple_delimiter}ANTI-COMPETITIVE PRACTICES{tuple_delimiter}TRUE{tuple_delimiter}2022-01-10T00:00:00{tuple_delimiter}2022-01-10T00:00:00{tuple_delimiter}Company A was found to engage in anti-competitive practices because it was fined for bid rigging in multiple public tenders published by Government Agency B according to an article published on 2022/01/10{tuple_delimiter}According to an article published on 2022/01/10, Company A was fined for bid rigging while participating in multiple public tenders published by Government Agency B.)
 {completion_delimiter}

 Example 2:
 Entity specification: Company A, Person C
 Claim description: red flags associated with an entity
 Text: According to an article on 2022/01/10, Company A was fined for bid rigging while participating in multiple public tenders published by Government Agency B. The company is owned by Person C who was suspected of engaging in corruption activities in 2015.
 Output:

 (COMPANY A{tuple_delimiter}GOVERNMENT AGENCY B{tuple_delimiter}ANTI-COMPETITIVE PRACTICES{tuple_delimiter}TRUE{tuple_delimiter}2022-01-10T00:00:00{tuple_delimiter}2022-01-10T00:00:00{tuple_delimiter}Company A was found to engage in anti-competitive practices because it was fined for bid rigging in multiple public tenders published by Government Agency B according to an article published on 2022/01/10{tuple_delimiter}According to an article published on 2022/01/10, Company A was fined for bid rigging while participating in multiple public tenders published by Government Agency B.)
 {record_delimiter}
 (PERSON C{tuple_delimiter}NONE{tuple_delimiter}CORRUPTION{tuple_delimiter}SUSPECTED{tuple_delimiter}2015-01-01T00:00:00{tuple_delimiter}2015-12-30T00:00:00{tuple_delimiter}Person C was suspected of engaging in corruption activities in 2015{tuple_delimiter}The company is owned by Person C who was suspected of engaging in corruption activities in 2015)
 {completion_delimiter}

 -Real Data-
 Use the following input for your answer.
 Entity specification: {entity_specs}
 Claim description: {claim_description}
 Text: {input_text}
 Output:"""

wenben = {
  "agents_interactions": [
    {
      "date": "2023-08-02",
      "prompt": "你是一名ETH加密货币交易分析师。以下是按时间顺序给出的最新价格及辅助信息：\n\"\"\"\n开盘价：1857.28，总交易量：1048101，总消耗Gas：108139000000.0，成功交易：1021838，总转账价值：1.1722e+24，独立地址数：575885，平均Gas价格：27661560032.0，MACD信号：买入\n开盘价：1869.75，总交易量：1068108，总消耗Gas：108014000000.0，成功交易：1041275，总转账价值：1.31711e+24，独立地址数：507173，平均Gas价格：27190618467.0，MACD信号：买入\n\"\"\"\n请用一段简洁的文字分析近期信息并据此预测市场趋势。",
      "response": "近期数据显示ETH价格从1857.28美元小幅上涨至1869.75美元，交易活动强劲支撑了这一趋势——总交易量从104.8万笔增至106.8万笔，成功交易量也同步上升。总转账价值增长约12.3%（从1.1722e+24增至1.31711e+24），表明网络使用率提高，尽管独立地址数下降约12%，暗示大额持有者正在整合。Gas指标保持稳定（总消耗Gas微降，平均Gas价格下降约1.7%），反映出网络需求效率良好。持续的",
      "Sender": "链上分析师代理",
      "Recipient": [
        "多头交易员代理",
        "空头交易员代理"
      ]
    },
    {
      "date": "2023-08-02",
      "prompt": "你是一名ETH加密货币交易分析师。你需要分析以下新闻：\n\"\"\"\n[{'id': 16, 'time': '2023年8月1日 GMT+8 15:00:00', 'title': 'DeFi平台Term Finance将固定利率借贷引入以太坊 - CoinDesk', 'content': '“你所做的是批量流动性，”Chu说。“因为AMM是持续、连续的流动性，所有操作都是异步进行的，可能需要一些时间才能达到平衡，这其中存在一定的博弈性。这种批量流动性的方式对新兴市场非常有利，也非常常见。如果你想想纽约证券交易所，它最初其实是每日拍卖的形式，而不是我们现在所知的连续市场。”'}, {'id': 10, 'time': '2023年8月1日 GMT+8 15:00:00', 'title': '以太坊跻身全球前50大资产，比特币市值升至全球第12位 - CryptoSlate', 'content': '快讯\\n\\n以太坊以2230亿美元的市值进入全球前50大资产行列，超越了钯金，距离铂金仅差170亿美元，这标志着其在全球资产中的显著上升。\\n\\n比特币则以约5700亿美元的市值保持全球第12大资产的排名。尽管距离顶级资产组（如科技股和金银等贵金属）仍有较大差距，但其地位依然稳固。\\n\\n这些进展凸显了加密货币对全球金融舞台日益增长的影响力。特别是以太坊的上升轨迹反映了市场正在演变，其地位正与传统贵金属等资产并列。\\n\\n投资者和市场观察者很可能会继续密切关注比特币和以太坊的进展，因为它们正在挑战传统资产类别，并可能重塑全球市值排名。'}, {'id': 20, 'time': '2023年8月1日 GMT+8 15:00:00', 'title': 'Cardano创始人宣称其交易成本优于以太坊 - U.Today', 'content': 'Cardano创始人Charles Hoskinson最近强调了Cardano在交易成本上相对于以太坊的经济优势。\\n\\n广告\\n\\n这位加密货币大佬引用了一条推文，称Cardano网络上56笔资产的交易成本仅为0.14美元，这一费用是以太坊无法比拟的。\\n\\n他在推文中暗示，低成本可能归功于Cardano区块链架构的独特特性，包括扩展UTXO（EUTXO）、原生资产及其智能合约语言Plutus。\\n\\n扩展UTXO是Cardano的底层账本模型，而Plutus是该平台的智能合约语言，为去中心化应用（DApp）提供支持。这些设计旨在确保安全且成本高效的交易，这对区块链网络的整体效用和采用至关重要。\\n\\n较低的交易成本是Cardano与以太坊之间的主要争议点，后者在网络拥堵期间的高额交易费用屡遭批评。被称为“日本以太坊”的Cardano旨在提供一个更易用、更经济且无需许可的区块链。\\n\\nHoskinson的言论与他此前对以太坊的批评一致，他曾质疑这个他参与创建的区块链的质押机制。\\n\\n他经常对比两个平台的方案，暗示Cardano的权益证明协议设计更有效，因此优于以太坊。\\n\\n在一系列尖锐评论中，Hoskinson还将以太坊描述为“垃圾场”，并暗示它可能被淘汰。'}, {'id': 6, 'time': '2023年8月1日 GMT+8 15:00:00', 'title': '以太坊ETF竞赛升温，六家资产管理公司向SEC提交申请 - U.Today', 'content': '广告\\n\\n彭博社高级ETF分析师Eric Balchunas及其同事James Seyffart总结了美国证券交易委员会（SEC）正在审查的所有以太坊（ETH）期货ETF申请。\\n\\nVanEck、ProShares、Grayscale等六家公司的以太坊ETF或将于10月获批\\n\\nBitwise、Roundhill、VanEck、ProShares和Grayscale于8月1日向SEC提交了以太坊期货ETF申请。Volatility Shares则于7月28日提交。因此，SEC目前正在处理六份以太坊期货ETF文件。\\n\\n这是以太坊期货ETF竞赛的最新进展，目前共有六家机构参与……感谢@JSeyff快速整理 pic.twitter.com/G1rwwfaFoS — Eric Balchunas (@EricBalchunas) 2023年8月1日\\n\\nProShares甚至提交了两项产品申请：ProShares做空以太坊策略ETF和ProShares以太坊策略ETF。\\n\\n这些数据由彭博社的Eric Balchunas在其推特账号分享。所有申请均标注为“等待批准”状态。SEC的最终决定截止日期为2023年10月中旬。\\n\\n评论区的用户也在等待资产管理规模达9万亿美元的巨头Blackrock加入这六家以太坊期货ETF先驱的行列。\\n\\n据U.Today此前报道，全球首只以“实物”以太坊（ETH）支持的交易所交易基金（ETF）已于2021年4月在加拿大推出。\\n\\n当地资产管理公司Purpose Investments（管理资产规模150亿美元）在多伦多证券交易所推出了该ETF。\\n\\n专家对比特币（BTC）以外的ETF持乐观态度\\n\\n加密货币的期货和现货ETF被视为投资领域的变革者。它们将消除企业实际存储比特币（BTC）和以太坊（ETH）的需求。\\n\\n因此，财富管理机构可以像投资常规交易所交易产品一样投资加密货币。这可能会引发资本涌入该领域，并可能成为加密货币牛市的催化剂。\\n\\n资深加密风投Vance Spencer曾强调，比特币现货ETF的推出将促使其他加密产品获批。\\n\\n他认为，这些重大公告最早可能于今年发布。'}, {'id': 18, 'time': '2023年8月1日 GMT+8 15:00:00', 'title': 'Term Finance将固定期限借贷协议引入以太坊主网 - Blockworks', 'content': '固定期限借贷协议Term Finance计划于今日在以太坊主网上线。\\n\\n该协议采用三方回购（repo）模型（传统银行业常用），支持非托管、固定利率的抵押借贷。\\n\\n三方回购是一种涉及三方的回购交易。在传统金融中，证券交易商通过短期回购管理自身及客户资产。而在Term Finance中，智能合约将取代证券交易商。\\n\\nTerm Finance背后的团队Term Labs联合创始人Billy Welch告诉Blockworks，其扩展固定期限借贷的能力使其区别于其他同类协议。\\n\\n目前许多固定期限借贷协议使用自动化做市商（AMM）和流动性池来发起固定期限贷款。\\n\\n“从借款人的角度来看，当你试图借入（例如）50万、100万或200万美元时，会出现严重的滑点，”Welch说。“这对机构级用户来说不可扩展。而从流动性提供者或贷款人的角度来看，只有当借款人与资金池互动时才能获得回报，因此实际年化收益率可能只有0.5%。”\\n\\nTerm Finance则采用双向盲拍流程，根据借款人和贷款人的报价进行匹配，Welch解释道。\\n\\n“我们通过拍卖让借款人和贷款人在一段时间内提交借贷报价。拍卖结束后，我们会根据供需曲线撮合交易。任何愿意以清算利率或更高利率借款的借款人将获得贷款，任何愿意接受清算利率或更低利率的贷款人将放贷。”\\n\\n通过这种模式，流动性提供者无需将资金无限期锁定在AMM中。\\n\\n“你只需在提交报价期间锁定资金。如果拍卖成功，你将获得贷款；如果没有，你可以取回代币并转入其他DeFi借贷协议，获取浮动利率收益，”Welch说。\\n\\n以太坊主网上的首次公开拍卖将是以wstETH为抵押品借入USDC的1个月期贷款，到期日为8月31日。拍卖将于北京时间今日23:00开始，参与者有48小时时间（截至周四23:00）提交报价。\\n\\n“我们的长期愿景是拥有每周、每月甚至一年到期的回购代币。这可以成为DeFi原生基准收益率曲线的基础，”Welch说。\\n\\n不要错过下一个大事件——订阅我们的免费每日通讯。'}]\n\"\"\"\n请用一段简洁的文字分析新闻并据此预测市场趋势。",
      "response": "新闻突出了多个以太坊（ETH）的利好因素。六家大型资产管理公司（包括VanEck和Grayscale）竞相申请以太坊期货ETF，表明机构兴趣增长，若10月获批可能为ETH带来资金流入。此外，以太坊以2230亿美元市值进入全球前50大资产，反映了其作为主流资产的地位正在增强。Term Finance在以太坊主网推出固定利率借贷协议等创新可能提升DeFi的实用性，通过可扩展的拍卖流动性模型吸引机构借款人。然而，Cardano创始人Charles Hoskinson强调的更低交易成本可能对以太坊构成压力，迫使其解决",
      "Sender": "新闻分析师代理",
      "Recipient": [
        "多头交易员代理",
        "空头交易员代理"
      ]
    },
    {
      "date": "2023-08-02",
      "prompt": "你是一名ETH加密货币交易分析师。以下是按时间顺序给出的你的分析和操作历史：\n\"\"\"\n日收益率：\n0.0\n操作：\n0.3\n日收益率：\n0.002014064417589667\n\"\"\"\n请反思近期表现，并从宏观层面指导未来交易，指出市场中的关键趋势和指标。",
      "response": "### 近期表现反思：\n\n1. **初始操作（0.3）：**  \n   - 你的第一个操作是将30%的仓位配置到ETH（假设“0.3”代表30%仓位）。这是一个相对保守的开仓动作，表明你持谨慎乐观态度或在等待趋势确认。  \n   - 随后的**日收益率0.002014（0.2%）**虽然为正，但增幅有限，表明市场在此期间相对平稳或略微看涨。  \n\n2. **市场背景：**  \n   - 较低的日收益率表明市场波动性低或缺乏",
      "Sender": "反思分析师代理",
      "Recipient": [
        "多头交易员代理",
        "空头交易员代理"
      ]
    },
    {
      "date": "2023-08-02",
      "prompt": "你是一名看涨ETH的加密货币交易员。你专注于分析师报告中的有利条件。请总结这些条件并提出交易建议。\n\"\"\"\n\n",
      "response": "### **ETH看涨交易总结与行动计划**  \n\n#### **支持ETH看涨的有利条件：**  \n1. **宏观与机构采用**  \n   - 机构兴趣增加（ETF获批、对冲基金配置）。  \n   - EIP-1559后ETH的通缩供应（随着使用量增加，净供应减少）。  \n\n2. **链上指标**  \n   - 交易所储备下降（潜在供应紧缩）。  \n   - 质押参与率上升（超过25%的供应量被锁定，减少抛压）。  \n\n3. **技术面强势**  \n   - ETH/BTC交易对表现强劲",
      "Sender": "多头交易员",
      "Recipient": [
        "最终交易员代理"
      ]
    },
    {
      "date": "2023-08-02",
      "prompt": "你是一名看跌ETH的加密货币交易员。你专注于分析师报告中的不利条件。请总结这些条件并提出交易建议。\n\"\"\"\n\n",
      "response": "### **ETH看跌总结（不利条件）**  \n\n1. **宏观经济压力**  \n   - 利率上升和传统市场的避险情绪减少了加密市场的资金流入。  \n   - 美元走强削弱了对ETH等投机性资产的需求。  \n\n2. **链上指标疲软**  \n   - 网络活跃度下降（每日交易量减少，Gas费接近年度低点）。  \n   - DeFi总锁仓量（TVL）停滞或下降，表明实用需求减少。  \n   - 交易所流入增加（潜在抛压）。  \n\n3. **技术面破位**  \n   - ETH未能守住",
      "Sender": "空头交易员",
      "Recipient": [
        "最终交易员代理"
      ]
    },
    {
      "date": "2023-08-02",
      "prompt": "你是最终决策者。请综合考虑看涨和看跌报告，得出最合理的市场趋势结论并执行最终交易决策。请提供一个范围为[-1,1]的1位小数浮点数作为决策，反映你对市场趋势的信心和战略风险管理。\n\"\"\"\n\n",
      "response": "鉴于需要根据看涨和看跌报告做出最终交易决策，我将综合分析信息并给出信心加权的决策。具体方法如下：\n\n1. **评估报告**： \n   - 如果看涨报告在强度和可信度上压倒看跌报告，则决策偏向正值。\n   - 如果看跌报告更具说服力，则决策偏向负值。\n   - 如果势均力敌或不明朗，则决策为中性或谨慎加权。\n\n2. **信心与风险管理**：\n   - 数值范围从-1（强烈看跌）到+1（强烈看涨），0为中性",
      "Sender": "最终交易员代理",
      "Recipient": null
    }
  ],
  "performance_metrics": {
    "total_return": -0.003111692925611531,
    "sharpe_ratio": -0.43500692145845177,
    "daily_return_mean": -0.15506950353733617,
    "daily_return_std": 0.3564759452963029,
    "last_action": 0.0
  }
}
wenben_str = json.dumps(wenben, indent=2)



##第二种deepseek的调用方式
llm = ChatOpenAI(
    model='deepseek-chat',
    openai_api_key=deepseek_api_key,
    openai_api_base='https://api.deepseek.com/v1',
    max_tokens=4096,
    temperature=1.5
)


# 2、定义提示模版
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate(
    [
        (
            "system",
            EXTRACT_CLAIMS_PROMPT,
        ),
        ("human", "{input}"),
    ]
)

chain = prompt | llm
res = chain.invoke(
        {

            "input": wenben_str,
        }
)
print(res.content)



