from IPython.utils import io
from serpapi import GoogleSearch
import os
import string
import re


import openai
import urllib.request, json

from langchain_openai import ChatOpenAI
from serpapi import GoogleSearch
from dotenv import load_dotenv
from IPython.utils import io
# from Multimodal_gemini import ImageAnalyzer



load_dotenv(r'D:\硕士\aiagent\gpt_CWSC\.env',override=True) # 关键：`override=True`

openai_api_key = os.getenv("OPENAI_API_KEY")
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY_2")
print(deepseek_api_key)
print(openai.api_key)


template = """你是短视频平台文案编辑专家，现在需要你根据提供的产品资料、产品受众、产品卖点、文案主题和文案类型，充分理解并分析品牌的调性、产品的特点和卖点，学习示例的风格，为品牌撰写一条由标题和置顶评论两部分组成的文案。
## 根据提供的信息：产品名称是{Product_Information}、产品的受众人群是{Product_Audience}、产品的核心卖点(也是最重要的一点)是{Product_Selling_Points}以及文案主题是{Copy_Theme}，充分理解并分析品牌的调性、产品的特点和卖点，为{Product_Information}生成一条标题和相应的正文文案。
#产品补充背景
{Additional_Resource}
#你需要遵循的创作技能如下：
1.标题：
- 内容要求：准确、简明地传达视频的核心主题，通过标题突出品牌的独特调性和个性。可以使用具象化的意象来呈现品牌或产品的特点，让用户感受到品牌的独特氛围。
- 语气风格：根据品牌调性（例如奢华、高端、创新、自由等）调整语气，确保文案风格鲜明个性化，力求语言具体且生动。避免使用空泛、抽象的语言，避免冗长复杂。参考以下示例，感受品牌的语气风格。
2.置顶评论：
- 字数要求：不超过60字，包括标点符号。
- 内容要求：介绍主题或产品的核心特点，通过第一句呈现品牌的理念和产品的个性，具象化描述产品背后的理念或使用场景，帮助用户在情感上产生共鸣，如果有明星代言，利用明星效应，共鸣效果更是锦上添花！。第二句引导用户关注品牌或前往官网，探索更多信息。
- 语气风格：保持正式、优雅的语气，以品牌推广为导向，不要使用过于口语化的表达。
3.修改：
-语句通顺，逻辑清晰，前后衔接自然。
## 示例:
- 示例1：
标题：焕新旅途，夏日造型拍档已上线！
置顶评论：
路易威登 Flight Mode 系列沿袭经典制箱工艺，呈演春季探险旅途。欢迎登陆官网，探索更多心仪单品。
- 示例2：
标题：
跟随浪花节拍，奔赴自由海岸。
置顶评论：
随风起航，赴往路易威登 2024 早秋男士系列的航海旅程。邀您登陆官网，揭晓新季潮流理念。
示例3：标题：
沉浸浩瀚沙海，探秘东方之息。
置顶评论：
路易威登浮影 Ombre Nomade 香氛以沉香木为主调，交融安息香和树莓香调，还原沙漠中的香息奇境。邀您前往新品体验店，唤启嗅觉奇旅。
示例4：标题：
漫步海滩，轻嗅自由之息。
置顶评论：
路易威登追梦（Attrape-Rêves）女士香水交融可可与牡丹花的馥郁气息，带来迷人芬芳。邀您前往新品体验店，探索更多芳香佳作。

"""









llm_gpt4o = ChatOpenAI(model="gpt-4o", temperature=1.0,api_key = openai_api_key,base_url='https://xiaoai.plus/v1',top_p=0.7)

llm_deepseek = ChatOpenAI(
    model='deepseek-chat',
    openai_api_key=deepseek_api_key,
    openai_api_base='https://api.deepseek.com/v1',
    max_tokens=100,
    temperature=1.5
)
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate.from_template(template)
# 定义字典
prompt_dict = {
    "产品名称": "路易威登Blossom春夏手袋系列",
    "受众人群": """
    寻求轻盈、优雅且具永恒魅力时尚单品的消费者，特别是对路易威登品牌历史、精湛工艺和标志性手袋感兴趣的女性
    """,
    "核心卖点": """
    路易威登春夏手袋系列的核心卖点在于其融合了轻盈、优雅与永恒的设计 ，并巧妙地将品牌历史传承与现代美学 相结合。
    """,
    "文案主题": """1.路易威登春夏皮具系列广告文案主题围绕“轻盈与优雅的航海之旅”展开，旨在通过多平台数字和线下策略，提升品牌在社交媒体的可见度。
    2.文案核心理念强调唤起轻盈、季节性的优雅，呈现一个融入彩色与图形点缀的永恒衣橱 。它通过描绘宁静海景和品牌“逃逸精神”与“旅行艺术”的精髓，
    展现标志性手袋的魅力与重新诠释 。
    文案将聚焦Capucines的永恒优雅和柔软质感 ，GO-14的现代风格与标志性细节 ，以及Side Trunk对手工技艺与多功能性的致敬 
    """,

}

# analyzer = ImageAnalyzer(
#         env_path=r'D:\硕士\aiagent\gpt_CWSC\.env',
#         model_name="gemini-2.0-flash",
#         temperature=0,
#         timeout=30
#     )
#
# print("\n" + "=" * 50)


# prompt1 = "使用中文描述这些图片的{}产品，字数大概300左右".format(prompt_dict['Product_Information'])
#
# print("\n=== 示例4: 分析多张本地图片（简化列表） ===")
# local_images = [
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\1.png",
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\2.png"  # 假设存在
#             ]
#
# result = analyzer.analyze_image(local_images, prompt=prompt1,is_local= True)
# if result["success"]:
#     print("分析结果:", result["result"])
#     print(f"处理图片数: {result['processed_images_count']}/{result['total_images_count']}")
# else:
#     print("分析失败:", result["error"])






#additional_data2 =  result["result"]
#additional_data2 = """路易威登Blossom珠宝系列于2023年10月推出其“白金动画”系列 。该系列由品牌代言人Ana de Armas倾情演绎 ，以金与钻石的璀璨融合重新诠释了多面优雅的内涵 。该系列作品由Francesca Amfitheatrof设计 ，以Monogram花卉的轮廓为灵感 ，推出可无限叠戴的新款白金珠宝 ，并饰以璀璨钻石 。系列分为“LATEST CREATIONS”、“IDYLLE BLOSSOM”和“COLOR BLOSSOM”三个主题，展现了弯曲抽象的切割、诗意的设计以及自然界中大胆的色彩 。推广将通过线上（官网、Instagram、Facebook、X、Pinterest、微博、Kakao、RED、LINE、CRM邮件、付费搜索）和线下（户外广告、平面媒体、新闻稿）多渠道进行 。广告文案可强调其独特的百搭性、钻石点缀的璀璨以及抽象的光辉，并引导消费者“探索该系列” """

additional_data2 = """
以下是从文件中提取的、有助于撰写路易威登春夏手袋系列广告文案的内容：

核心主题与语调：

放大社交媒体上的可见度。
https://www.google.com/search?q=%E5%9C%A8LV.com和LV APP上提供沉浸式体验。
通过产品焦点邮件和推送通知，https://www.google.com/search?q=%E6%8F%90%E5%8D%87LV.com和LV APP的流量。
“唤起轻盈、季节性的优雅，全新的航海系列概括了一个轻盈的永恒衣橱，融入了彩色、图形化的点缀。”
“以宁静海景为背景，最新的女性系列揭示了路易威登重新构想的标志性手袋的交响曲，充满了优雅的魅力。”
“在反映品牌逃逸精神的氛围中捕捉，标志性的GO-14手袋展示了路易威登历史性malletage绗缝的现代重新诠释。”
“唤起路易威登标志性旅行箱制作的精湛技艺，Side Trunk手袋结合了精湛的美学和多功能性，适合随身旅行风格。”
“Capucines手袋将休闲的现代美学与日常多功能性融合，新款Capucines GM Souple以超大帆布和皮革轮廓重新诠释了路易威登的标志，展现出更加柔和的质感。”
“Capucines手袋体现了品牌对冒险的追求，超越了皮具的限制，从微型精致到通过艺术合作实现的宏大创意愿景。”
“向品牌历史悠久的旅行艺术致敬，最新的女性系列……”
Side Trunk的文案：受品牌旅行箱制作传统的启发，精致的Side Trunk饰有金属S锁和包角。这款多功能包款以柔和、永恒的色调呈现，配有可拆卸手柄和肩带，适合日常造型。
关键产品亮点：

Capucines:
“永恒优雅的缩影”。
“以各种对比色和尺寸变化呈现”。
Capucines GM Souple：超大帆布和皮革轮廓，柔软度突出。
超越皮具限制，从微型精致到宏大创意愿景。
GO-14:
“标志性的LV扭锁和可拆卸的金色链条”。
“注入路易威登标志性元素与现代态度，GO-14手袋揭示了一系列动态色彩和尺寸变化”。
展示了路易威登历史性malletage绗缝的现代重新诠释。
Side Trunk:
“唤起路易威登标志性旅行箱制作的精湛技艺”。
“结合了精湛的美学和多功能性，适合随身旅行风格”。
受品牌旅行箱制作传统的启发，饰有金属S锁和包角。
多功能包款，以柔和、永恒的色调呈现，配有可拆卸手柄和肩带，适合日常造型。
宣传活动构成：

视觉素材:
9张真人模特图（5张Capucines，2张GO-14，2张Side Trunk）。
3张静物图。
视频素材:
1个主宣传片（约45秒）。
3个短片（10秒），分别聚焦Capucines、GO-14、Side Trunk。
2个主要短片（15秒）。
3个TikTok视频（10秒），分别聚焦Capucines、GO-14、Side Trunk。
传播平台：
社交媒体: Instagram (Grid, Stories), X (Twitter), TikTok, Douyin, Kuaishou, Facebook, Kakao, LinkedIn, YouTube (Main Film, Community Tab, Shorts), Pinterest, Instagram Threads, Weibo, RED, WeChat (Video Account, Mini Program), LINE.
自有平台: LV.COM & LV APP (专用营销活动落地页、产品焦点、重定向、新的PLP、更新现有落地页、首页及移动端推送、探索卡、商店卡).
CRM: 产品焦点邮件和推送通知.
线下: 纸媒（日报、周刊、月刊）+ 户外广告 (4月15日)。
新闻稿: 待定。
付费社交媒体: 全漏斗营销（意识、考虑、转化）。
发布时间：

主要发布日期：4月29日。
户外广告：4月15日。
付费社交媒体活动：4月29日至5月20日。
营销策略/目标：

提升Capucines和GO-14的关注度，尽管它们不是在线销售产品，鼓励客户前往门店或联系客户服务中心。
提高Side Trunk的知名度并使其成为标志性产品。
浓缩版本：路易威登春夏手袋系列广告文案核心要素

路易威登春夏手袋系列广告旨在通过多渠道数字和线下策略，提升品牌在社交媒体上的可见度，https://www.google.com/search?q=%E5%B9%B6%E9%A9%B1%E5%8A%A8LV.com和LV APP的流量。

核心理念： 营销活动以宁静的海景为背景，通过重新构想的标志性手袋，展现出轻盈、季节性的优雅和永恒的魅力，同时融入彩色、图形化的点缀。它强调品牌“逃逸精神”和“旅行艺术”的精髓。

产品亮点：

Capucines: 象征着永恒的优雅，以多种色彩和尺寸呈现，新的GM Souple款尤其突出其柔软质感。它体现了品牌对冒险的追求，超越了皮具的界限。
GO-14: 融合了现代态度与品牌标志性元素，拥有标志性的LV扭锁和可拆卸链条，并重新诠释了历史性的malletage绗缝工艺。
Side Trunk: 灵感源自品牌旅行箱制作传统，以金属S锁和包角为特色，展现了精湛美学与多功能性，适合日常与旅行风格。
传播策略： 此次活动将通过9张真人模特图、3张静物图、1部主宣传片（45秒）、5部短片（10-15秒）和3个TikTok专属视频进行推广。宣传将覆盖Instagram (贴文、快拍、Threads)、X (Twitter)、TikTok、Douyin、Kuaishou、Facebook、Kakao、LinkedIn、YouTube、Pinterest以及微信 (视频号、小程序) 和LINE等全球及本土社交媒体平台。同时，活动也包括付费社交媒体投放、纸媒和户外广告，https://www.google.com/search?q=%E5%B9%B6%E5%9C%A8LV.com和LV APP上设置专用落地页、产品详情页，并辅以产品焦点邮件和推送通知进行CRM沟通。

目标： 针对Capucines和GO-14，旨在提升关注度并引导客户前往门店或联系客服。对于Side Trunk，目标是提高其知名度并将其打造为标志性产品。"""
print(additional_data2)
prompt_dict['Additional_Resource'] = additional_data2

# 使用字典填充


text1 = """
角色：一名产品受众人群分析师。

指令：根据补充资料细化人类给出的的受众人群，字数大概在20字左右,只需返回最重要的结果即可，优化后的结果以双引号输出。

补充资料：{additional_data}

"""
#
Product_Audience_prompt = ChatPromptTemplate(
    [
        (
            "system",
            text1
        ),
        ("human", "{input}"),
    ]
)
prompt_dict['Product_Audience'] =llm_deepseek.invoke(Product_Audience_prompt.format(additional_data = additional_data2,input=prompt_dict['Product_Audience'])).content

prompt = prompt.format(**prompt_dict)
print(prompt)
res = llm_gpt4o.invoke(prompt)
print(res.content)


