import os

from langchain_openai import OpenAI, ChatOpenAI
from dotenv import load_dotenv


# 加载环境变量
load_dotenv()
openai_api_key = "sk-AzNvIyV5M4bMqK1I84802e7833B849F1Be495374Db9f419c"

#llm = ChatOpenAI(model="gemini-2.0-flash", temperature=0,api_key = "sk-AzNvIyV5M4bMqK1I84802e7833B849F1Be495374Db9f419c",base_url='https://xiaoai.plus/v1')
# 2、定义提示模版

llm = ChatOpenAI(model="GPT-4.1", temperature=0,base_url='https://ai.hao666666.online/openai')

from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate(
    [
        (
            "system",
            "You are a helpful assistant that translates {input_language} to {output_language}.",
        ),
        ("human", "{input}"),
    ]
)

chain = prompt | llm
res = chain.invoke(
    {
        "input_language": "English",
        "output_language": "chinese",
        "input": "I love programming.",
    }
)

res1 = llm.invoke("你的开发者是谁")
print(res)
print(res1)

