From: <PERSON> (<EMAIL>)
Subject: Re: Word attachments in Mutt
Newsgroups: comp.mail.mutt
Date: 2001-05-16 01:21:11 PST

<PERSON> <<EMAIL>> wrote:

> I receive MS Word attachments in Mutt reguarly. Is there a way to
> read these via <PERSON><PERSON> in a Linux/Solaris environment? (e.g. Lynx or
> some type of viewer)?

The best M$-word to ASCII converter has to be antiword!

Just put the following line into .mailcap:

application/msword; antiword %s; copiousoutput

Sven

--
"We just typed make"
(<PERSON>, Director of Server Product Marketing at Informix
                                      about porting their Database to Linux)
/me is giggls@ircnet, http://geggus.net/sven/ on the Web
