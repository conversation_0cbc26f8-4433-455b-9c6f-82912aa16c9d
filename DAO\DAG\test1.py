import networkx as nx
from itertools import combinations, chain
import math


def powerset(iterable):
    """返回集合的所有子集"""
    s = list(iterable)
    return chain.from_iterable(combinations(s, r) for r in range(len(s) + 1))


def general_sv(G, coalition_values):
    """
    计算连接性博弈中每个节点的Shapley值
    """
    nodes = list(G.nodes())
    n = len(nodes)
    shapley_values = {node: 0 for node in nodes}

    # 遍历所有可能的子集
    for C in powerset(nodes):
        C = set(C)
        if not C:  # 跳过空集
            continue

        # 检查子集是否连通
        if len(C) == 1 or nx.is_connected(G.subgraph(C)):
            # C是连通的
            C_tuple = tuple(sorted(C))
            if C_tuple not in coalition_values:
                continue  # 如果没有提供该联盟的值，则跳过

            for node in nodes:
                if node not in C and any(G.has_edge(node, c_node) for c_node in C):
                    # 节点是C的邻居
                    C_with_node = tuple(sorted(C | {node}))
                    if C_with_node in coalition_values:
                        # 计算边际贡献
                        marginal = coalition_values[C_with_node] - coalition_values[C_tuple]

                        # 计算系数ξC
                        coef = 1 / (n * (2 ** (n - 1)))

                        shapley_values[node] += coef * marginal

    return shapley_values


def traditional_shapley(coalition_values, nodes):
    """
    计算传统的Shapley值(不考虑图的连通性)
    """
    n = len(nodes)
    shapley_values = {node: 0 for node in nodes}

    for node in nodes:
        # 考虑所有不包含当前节点的子集
        for S in powerset([n for n in nodes if n != node]):
            S = set(S)
            S_tuple = tuple(sorted(S))

            # 添加当前节点到子集
            S_with_node = tuple(sorted(S | {node}))

            # 如果两个联盟都有值，计算边际贡献
            if S_tuple in coalition_values and S_with_node in coalition_values:
                # 计算排列系数
                s_size = len(S)
                coef = math.factorial(s_size) * math.factorial(n - s_size - 1) / math.factorial(n)

                # 计算边际贡献
                marginal = coalition_values[S_with_node] - coalition_values[S_tuple]

                shapley_values[node] += coef * marginal

    return shapley_values


def main():
    # 用户输入边的列表
    print("请输入图的边，格式为: [('a','b'), ('b','c'), ...]")
    edges_input = input()
    edges = eval(edges_input)

    # 创建图
    G = nx.Graph()
    G.add_edges_from(edges)
    nodes = list(G.nodes())

    print(f"图包含节点: {nodes}")

    # 用户输入联盟的值
    print("请输入联盟的值，格式为: {('a',): value, ('a','b'): value, ...}")
    coalition_values_input = input()
    coalition_values = eval(coalition_values_input)

    # 定义总价值为所有单节点价值的总和
    total_value = sum(coalition_values.get((node,), 0) for node in nodes)

    # 计算连接性博弈的Shapley值
    connectivity_sv = general_sv(G, coalition_values)

    # 计算传统的Shapley值
    traditional_sv = traditional_shapley(coalition_values, nodes)

    print("\n连接性博弈中各节点的Shapley值(百分比):")
    for node, value in connectivity_sv.items():
        percentage = (value / total_value) * 100
        print(f"节点 {node}: {value:.4f} ({percentage:.2f}%)")

    print("\n传统方法下各节点的Shapley值(百分比):")
    for node, value in traditional_sv.items():
        percentage = (value / total_value) * 100
        print(f"节点 {node}: {value:.4f} ({percentage:.2f}%)")


if __name__ == "__main__":
    main()