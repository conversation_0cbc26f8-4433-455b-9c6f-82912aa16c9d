// script.js

document.getElementById('product-description').addEventListener('dragover', function(event) {
    event.stopPropagation();
    event.preventDefault();
    event.dataTransfer.dropEffect = 'copy';
    this.classList.add('dragover');
});

document.getElementById('product-description').addEventListener('dragleave', function(event) {
    this.classList.remove('dragover');
});

var file
document.getElementById('product-description').addEventListener('drop', function(event) {
    event.stopPropagation();
    event.preventDefault();
    this.classList.remove('dragover');
    var files = event.dataTransfer.files;
        if (files.length > 1) {
    alert('每次只能上传一个文件！');
    return;}

     file = files[0]; // 因为只允许一个文件，所以直接取第一个

    if (file.size > 52428800) { // 50MB 的字节数
        alert('文件大小不能超过50MB！');
        return;
    }
    $('#product-description').prop('disabled', true);
    $('#product-description').attr('placeholder', '');
    $('#product-description').css("background-color","#e0e0e0")
    $("#select-mode").css("display","block");

});

function draginfo(){
    $('#product-description').attr('placeholder', '请在此处填写产品相关信息及特点,可拖动文件至此窗口以识别文档内部信息');
    $("#select-mode").css("display","none");
    $('#loading-spinner').css("display","inline-block");
    var formData = new FormData();
    formData.append("file",file)
    var initiallySelectedValue = $('input[name="singleOption"]:checked').val();
    formData.append("select",initiallySelectedValue)
    $.ajax({
                url: 'productinfo/',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                dataType: 'json',
                headers: {
                    'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(data) {
                    $('#loading-spinner').hide();
                    $('#product-description').prop('disabled', false);
                    $('#product-description').css("background-color", "");
                    if(data.status === 1){
                        $('#product-description').val(data.response)
                    }else if(data.status ===0){
                        $('#loginModal').modal('show');
                    }else if(data.status ===-2){
                        alert("上传文件不能为空！")
                    }
                    file = null;
                },
                error: function() {
                    $('#loading-spinner').hide();
                    $('#product-description').prop('disabled', false);
                    $('#product-description').css("background-color", "");
                    file = null;
                }
            });

    // 如果需要上传文件，可以在这里添加上传逻辑
}