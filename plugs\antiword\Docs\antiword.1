.TH ANTIWORD 1 "Oct 29, 2005" "Antiword 0.37" "Linux User's Manual"
.SH NAME
antiword - show the text and images of MS Word documents
.SH SYNOPSIS
.B antiword
[
.I options
]
.I wordfiles
.SH DESCRIPTION
.I Antiword
is an application that displays the text and the images of Microsoft Word
documents.
.br
A wordfile named - stands for a Word document read from the standard input.
.br
Only documents made by MS Word version 2 and version 6 or later are supported.
.SH OPTIONS
.TP
.BI "\-a " papersize
Output in Adobe PDF form. Printable on paper of the specified size: 10x14,
a3, a4, a5, b4, b5, executive, folio, legal, letter, note, quarto, statement
or tabloid.
.TP
.B \-f
Output in formatted text form. That means that bold text is printed like
*bold*, italics like /italics/ and underlined text as _underlined_.
.TP
.B \-h
Give a help message.
.TP
.BI "\-i " "image level"
The image level determines how images will be shown.
.RS
.TP 3
0:
Use non-standard extensions from Ghostscript. This output may not print on
any PostScript printer, but is useful in case no hard copy is needed. It is
also useful when Ghostscript is used as a filter to print a PostScript file to
a non-PostScript printer.
.TP 3
1:
Show no images.
.TP 3
2:
PostScript level 2 compatible. (default)
.TP 3
3:
PostScript level 3 compatible. (EXPERIMENTAL, Portable Network Graphics (PNG)
images are not printed correctly)
.RE
.TP
.BI "\-m " "mapping file"
This file is used to map Unicode characters to your local character set.
The default mapping file depends on the locale.
.TP
.BI "\-p " papersize
Output in PostScript form. Printable on paper of the specified size: 10x14,
a3, a4, a5, b4, b5, executive, folio, legal, letter, note, quarto, statement
or tabloid.
.TP
.B \-r
Include text removed by the revisioning system.
.TP
.B \-s
Include text with the so-called "hidden text" attribute.
.TP
.B \-t
Output in text form. (default)
.TP
.BI "\-w " width
In text mode this is the line width in characters. A value of zero puts an
entire paragraph on a line, useful when the text is to used as input for
another wordprocessor. This value is ignored in PostScript mode.
.TP
.BI "\-x " "document type definition"
Output in XML form. Currently the only document type definition is db
(for DocBook).
.TP
.B \-L
In PostScript mode: use landscape mode.
.RE
.SH FILES
.TP
Mapping files like 8859-1.txt
.br
Antiword looks for its mapping files in three directories, in the order given:
.br
(1) The directory specified by $ANTIWORDHOME
.br
(2) The directory specified by $HOME/.antiword
.br
(3) Directory /usr/share/antiword
.TP
The fontnames file
.br
Antiword will look for its fontname file in the same directories as used for the
mapping files.
.br
The fontnames file contains the translation table from font names used by MS
Word to font names used by PostScript.
.TP
NOTE:
.br
Antiword cannot tell the difference between a file that does not exist and a
file that cannot be opened for reading.
.SH ENVIRONMENT
Antiword uses the environment variable ``ANTIWORDHOME'' as the first directory
to look for its files. Antiword uses the environment variable ``HOME'' to find
the user's home directory. When in text mode it uses the variable ``COLUMNS''
to set the width of the output (unless overridden by the -w option).

Antiword uses the environment variables ``LC_ALL'', ``LC_CTYPE'' and ``LANG''
(in that order) to get the current locale and uses this information to
select the default mapping file.
.SH BUGS
Antiword is far from complete. Many features are still missing. Many images are
not shown yet. Some of the images that are shown, are shown in the wrong place.
PostScript output is only available in ISO 8859-1 and ISO 8859-2.
.SH WEB SITES
The most recent released version of Antiword is always available from:
.br
http://www.winfield.demon.nl/index.html
.br
or try
.br
http://antiword.cjb.net/
.SH AUTHOR
Adri van Os <<EMAIL>>
.br
or try <<EMAIL>>
.sp
R.F. Smith <<EMAIL>> and
.br
Sindi Keesan <<EMAIL>>
.br
contributed to this manual page.
.SH LICENSE
Antiword is free software; you can redistribute it and/or modify it
under the terms of the GNU General Public License as published by the Free
Software Foundation; either version 2 of the License, or (at your option)
any later version.

This program is distributed in the hope that it will be useful but WITHOUT
ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
more details.

You should have received a copy of the GNU General Public License along
with this program; if not, write to the Free Software Foundation, Inc.,
59 Temple Place, Suite 330, Boston, MA 02111-1307 USA
.SH ACKNOWLEDGEMENTS
Linux is a registered trademark of Linus Torvalds.
.br
Adobe, PDF and PostScript are trademarks of Adobe Systems Incorporated.
.br
Microsoft is a registered trademark and Windows is a trademark of Microsoft
Corporation.
