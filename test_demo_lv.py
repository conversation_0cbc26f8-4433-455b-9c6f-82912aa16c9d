from IPython.utils import io
from serpapi import GoogleSearch
import os
import string
import re


import openai
import urllib.request, json

from langchain_openai import ChatOpenAI
from serpapi import GoogleSearch
from dotenv import load_dotenv
from IPython.utils import io
# from Multimodal_gemini import ImageAnalyzer



load_dotenv(r'D:\硕士\aiagent\gpt_CWSC\.env',override=True) # 关键：`override=True`

openai_api_key = os.getenv("OPENAI_API_KEY")
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY_2")
print(deepseek_api_key)
print(openai.api_key)


template = """你是Louis Vuitton（LV）品牌的小红书文案编辑专家，请按照以下模式进行文案创作：
#小红书文案编辑专家的任务
## 根据提供的信息：产品名称是{Product_Information}、产品的受众人群是{Product_Audience}、产品的核心卖点(也是最重要的一点)是{Product_Selling_Points}以及文案主题是{Copy_Theme}，充分理解并分析品牌的调性、产品的特点和卖点，为{Product_Information}生成一条标题和相应的正文文案。
#产品补充背景
{Additional_Resource}
## 示例
### 珠宝系列示例1：
- 标题：✨光芒熠动，成就万能叠戴法则
- 正文：
路易威登 Blossom 珠宝系列推出全新白金款式，尽释耀然光彩。
��以标志性 Monogram 星形花朵图案为设计元素，镂空几何造型搭配白金钻石，塑造星芒闪耀的动人气质。
��创意叠戴自成瞩目焦点，流光掠影间演绎率性与摩登。
### 女装系列示例2：
- 标题：⚓️赴约海滨假日
- 正文：
��海风轻拂，纵享感官假日。
��路易威登 Nautical 系列优雅亮相，以水手元素为灵感，营造浪漫度假风情。
白色与海军蓝为系列主色调，满载自由气息，绽现灵动之姿。
��揭晓 GO-14 手袋美妙新色，续写愉悦心绪；
Capucines Mini 手袋缀饰条纹亮片，点亮休闲时光。
### 男装系列示例3：
- 标题：领取你的海岛 OOTD
- 正文：
��登陆灵感航线，点亮造型意趣。
路易威登 2024 早秋男士系列扬帆起航，以航海主题诠释旅行的自在畅想。
��男装创意总监 @Pharrell Williams 焕新标志性细节，以现代绅士廓形重塑经典水手制服，为新季衣橱注入盎然活力。
### 香水系列示例4：
- 标题：寻迹芳香，步入追梦之旅
- 正文：
��邂逅梦幻香息，传递恒久爱意。
��路易威登追梦（Attrape-Rêves）女士香水随风蔓延，满溢清香吹拂。
��调香大师 Jacques Cavallier Belletrud 交融可可与牡丹花香，勾勒无尽香韵，触动感官末梢。
��携芬芳之礼传递心意，捕捉心动频率
# 文案生成流程
1. 理解品牌与产品：分析品牌调性、明确目标受众需求。
2. 提炼卖点：提取独特卖点，确保传达品牌高级感。
3. 标题创作：标题创作：以简洁有力的短句或互动性词句呈现，吸引注意。标题使用高大上或情感化的表达，营造氛围感，增强吸引力。
4. 正文撰写：3-5句内容，简短流畅，符合品牌调性，✨可巧妙融入emoji点缀，增添灵动感与情感张力，以场景化语言传递产品魅力，突出设计灵感与独特气质。
5. 校对优化：确保文案符合所有要求，进行必要的修改。
"""









llm_gpt4o = ChatOpenAI(model="gpt-4o", temperature=1.0,api_key = openai_api_key,base_url='https://xiaoai.plus/v1',top_p=0.7)

llm_deepseek = ChatOpenAI(
    model='deepseek-chat',
    openai_api_key=deepseek_api_key,
    openai_api_base='https://api.deepseek.com/v1',
    max_tokens=100,
    temperature=1.5
)
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate.from_template(template)
# 定义字典
prompt_dict = {
    "Product_Information": "路易威登FLIGHT MODE系列",
    "Product_Audience": "追求品质生活、热爱旅行、注重个性时尚表达的路易威登忠实顾客及高净值消费者",
    "Product_Selling_Points": " 融合品牌旅行传承与现代时尚功能性，提供从皮袋到成衣的全方位高端旅行装备",
    "Copy_Theme": "借由“飞航模式”概念，邀请消费者以摩登、优雅的姿态，探索无尽旅程，连接品牌历史与当代生活",

}

# analyzer = ImageAnalyzer(
#         env_path=r'D:\硕士\aiagent\gpt_CWSC\.env',
#         model_name="gemini-2.0-flash",
#         temperature=0,
#         timeout=30
#     )
#
# print("\n" + "=" * 50)


# prompt1 = "使用中文描述这些图片的{}产品，字数大概300左右".format(prompt_dict['Product_Information'])
#
# print("\n=== 示例4: 分析多张本地图片（简化列表） ===")
# local_images = [
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\1.png",
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\2.png"  # 假设存在
#             ]
#
# result = analyzer.analyze_image(local_images, prompt=prompt1,is_local= True)
# if result["success"]:
#     print("分析结果:", result["result"])
#     print(f"处理图片数: {result['processed_images_count']}/{result['total_images_count']}")
# else:
#     print("分析失败:", result["error"])






#additional_data2 =  result["result"]
additional_data2 = """路易威登推出FLIGHT MODE系列

路易威登推出2024夏季Flight Mode系列，再度探索旅行艺术。本系列涵盖成衣、旅行皮具、鞋履和配饰，成就摩登出行的个性之选。

夏日将至，Flight Mode系列围绕活力四溢的Élysée Palace图案展开，并缀以路易威登标志性的复古风格旅行贴饰。该图案旨在致敬开设于香榭丽舍大街103号的巴黎首家豪华酒店，路易威登也将在这一地标性地点开展新项目。

大量硬箱构筑而成的布景令人想起过往旅程，以及路易威登在无数次冒险之旅中扮演的重要角色。Flight Mode系列连接过去和当下，将激动人心的时刻推至高潮。印有Élysée Palace Hotel字样邮票的Monogram帆布用于塑造Keepall手袋、配有小号护照夹的Side Trunk手袋和旅行箱。这款全新帆布再现了19世纪末硬箱采用的面料：棉麻混纺，搭配轻盈树脂涂层。本系列的其他单品亦是旅途良伴，例如：取材柔软粒面牛皮革的Low Key Hobo手袋、Low Key Shoulder手袋，配备全新储物内隔层和可拆卸腰包的OnTheGo Voyage手袋，结合贴合身形的大号廓形和舒适肩背设计的经典Capucines手袋，以及采用清新矿物蓝色调的精巧Blossom手袋。

Flight Mode系列成衣为时尚运动着装注入动感风尚和闲适意味，从容应对旅途中的各式情况。取材奢华或科技面料的连帽斗篷、派克大衣和束腰夹克，风格休闲的丹宁单品，廓形宽松的轻盈中间层服饰和泳衣，这些单品均可互相搭配，打造风格多样而均衡的衣橱。配套单品还包括：运动套装、Monogram压花针织衫、山羊绒套装和饰有细条纹的流畅剪裁单品，轻松塑造利落造型。取材柔美斜纹桑蚕丝的Mahina Monogram印花与大地色调相得益彰，取材针织、斜纹布或锦纶的Élysee Palace图案令必备单品释放绚烂个性。

Élysee Palace图案点亮新款Travel Trunk桑蚕丝方巾和束发带，其上点缀的贴饰下方呈现经典棕和暖沙色1 Rue Scribe硬箱的错视效果印花。

Flight Mode系列恰到好处地平衡运动风尚和休闲格调，其中鞋履系列包括：动感十足的LV Rush运动鞋，搭配标志性外底和灵感源自跑鞋的鞋面细节；饰有标志性路易威登钩扣的平底凉拖和凉鞋。Élysee Palace图案以锦纶印花的形式呈现于经典Pool Pillow Comfort平底凉拖，或以皮革贴饰的形式呈现于Lous露跟运动鞋。

取材18K金（白色）的LV Volt项链等高级珠宝和精选时尚珠宝为本系列增添璀璨华彩。源自GO-14手袋的LV字母标识化身耳环和吊坠项链，出自Nanogram Enamel系列的手链和戒指可单独佩戴，亦可叠戴。LV Iconic Louisa耳环和戒指饰有精致珍珠，缓缓流露典雅气息。

此外，Dailygram渔夫帽、LV Moon Metal Square太阳眼镜是无惧烈日照射的必备单品。Flight Mode系列囊括精致单品和典雅套装，邀您开启无尽时尚之旅。"""
print(additional_data2)
prompt_dict['Additional_Resource'] = additional_data2

# 使用字典填充


text1 = """
角色：一名产品受众人群分析师。

指令：根据补充资料细化人类给出的的受众人群，字数大概在20字左右,只需返回最重要的结果即可，优化后的结果以双引号输出。

补充资料：{additional_data}

"""
#
Product_Audience_prompt = ChatPromptTemplate(
    [
        (
            "system",
            text1
        ),
        ("human", "{input}"),
    ]
)
prompt_dict['Product_Audience'] =llm_deepseek.invoke(Product_Audience_prompt.format(additional_data = additional_data2,input=prompt_dict['Product_Audience'])).content

prompt = prompt.format(**prompt_dict)
print(prompt)
res = llm_gpt4o.invoke(prompt)
print(res.content)


