function submitCheck() {
    var content = $('#content').val();
    var materials = $('#materials').val();

    if (!content || !materials) {
        alert("文案内容和相关资料不能为空！");
        return;
    }

    // 使用AJAX将文案和资料发送到后台进行处理
    $.ajax({
        url: '/check_copywriting/',  // 路由地址
        type: 'POST',
        data: {
            content: content,
            materials: materials
        },
        headers: {
            'X-CSRFToken': $('meta[name="csrf-token"]').attr('content')
        },
        success: function (data) {
            if (data.status === 1) {
                $('#check_result').html(`
                    <h5>检查结果：</h5>
                    <p>${data.result}</p>
                `);
            } else {
                alert("文案检查失败，请稍后再试。");
            }
        },
        error: function () {
            alert("服务异常，请稍后重试。");
        }
    });
}
