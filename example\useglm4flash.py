from dotenv import load_dotenv
from langchain.schema import SystemMessage, HumanMessage, StrOutputParser

from langchain.prompts import ChatPromptTemplate, PromptTemplate
import os
from langchain_community.chat_models import ChatZhipuAI
from langchain_openai import ChatOpenAI

load_dotenv(r'D:\硕士\aiagent\gpt_CWSC\.env')
zhipu_api_key = os.getenv("ZHIPUAI_API_KEY")  # 从环境变量中获取DeepSeek API Key

# llm = ChatDeepSeek(
#     model="deepseek-ai/DeepSeek-V3",
#     temperature=0,
#     max_tokens=None,
#     timeout=40,
#     max_retries=2,
#     api_base="https://api.siliconflow.cn",
#     api_key=deepseek_api_key,
# )

##第二种deepseek的调用方式
llm = ChatZhipuAI(
    model='glm-4-flash',
    api_key="641760034d074410a789a8880e49571b.r88xnLl1lQvXl4Lg",

    max_tokens=4096,
    temperature=1.5
)


# 2、定义提示模版
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate(
    [
        (
            "system",
            "You are a helpful assistant that translates {input_language} to {output_language}.",
        ),
        ("human", "{input}"),
    ]
)

chain = prompt | llm
res = chain.invoke(
        {
            "input_language": "English",
            "output_language": "German",
            "input": "I love programming.",
        }
)
print(res.content)

