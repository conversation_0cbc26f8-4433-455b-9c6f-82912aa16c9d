from IPython.utils import io
from serpapi import GoogleSearch
import os
import string
import re


import openai
import urllib.request, json

from langchain_openai import ChatOpenAI
from serpapi import GoogleSearch
from dotenv import load_dotenv
from IPython.utils import io



load_dotenv(r'D:\硕士\aiagent\gpt_CWSC\.env')
openai.api_key = os.getenv("OPENAI_API_KEY")
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY_2")
print(deepseek_api_key)
print(openai.api_key)
serpapi_key = '61da5eaf67a09ac0ed1397dee8debd0f7749e6551c545be8bcedc572d6c3f35b'

template = """你是公众号平台文案编辑专家，现在你需要学习示例文案介绍产品的切入点与切入方式，为品牌撰写逻辑清晰、层次分明和高质量的文案撰写提供指导。
任务说明：请根据给定的产品名称、产品信息，产品卖点、文案主题，明星的代言信息进行思考创作。
#你的回答只需要包含你对文案撰写的大纲指导，不需要提供新的文案内容以及其它文字表述。
#<产品名称>{product_name}</产品名称>
#<产品信息>{product_introduction}</产品信息>
#<产品卖点>{product_selling_point}</产品卖点>
#<产品主题>{copy_theme}</产品主题>
#<明星代言信息>{extra_copy_requirements}</明星代言信息>
#<文案要求>
**风格与创意**
   - 风格需与品牌调性保持一致，如高端、优雅、创新等，语言生动优美。
   - 运用情感渲染、故事叙述、形象描绘、色彩表达、情感连接等方式介绍产品。
   - 保持创造力，必要时可发挥想象力补充信息，注意：补充需保持真实性，且以品牌调性或产品使用场景为导向。
</文案要求>
<示例文案>
**1.标题：低温前行，攀登无界
摘要：2023户外秋冬徒步系列全新登场
正文：地势变幻，不抵内心探索渴望
看见低温预警 向冬日山脉，发起新的挑战 

◎愈低温，愈徒步前行
SALOMON 2023全新户外秋冬徒步系列

逆风攀登，纵览山巅严寒光景
享受季节的反差感
看见一处与众不同的罕见风景

山野探寻 温暖随行

PATROLLER GORE-TEX 羽绒服
雨雪纷飞的季节，山地活动也未曾停歇
达到RDS认证的700蓬松度羽绒
配合双层透气的GORE-TEX面料
兼具强大保暖与防水性能
高领设计协同符合人体工程学的剪裁
为每一次穿着，提供舒适体验

PATROLLER 三合一羽绒服
寒冷潮湿的冬季，依旧与干爽相伴远行
外层使用防水面料
确保冬季环境下的干燥与温暖体验
羽绒服和外壳通过拉链链接
适配多元天气，实现三种穿着方式
极大程度满足多样户外场景 

ELIXIR ULTRA 羽绒大衣
与低温一同前行的，从来不是厚重与臃肿
整体采用大型羽绒腔室，羽绒占比高达90%
实现高性能的冬季保暖与防护功效
采取先进HeiQ XReflex热反射技术
增加超薄辐射隔热层的同时，不增加衣物自身重量
兼顾穿着体感与优越的保暖效果

徒步攀登 舒适向上
ELIXIR MID GORE-TEX
征服长距离，户外世界的每一处都是舞台
鞋面采用防水GORE-TEX薄膜
恰逢冬日雨水，一样无惧前行
Sensifit™中足包裹技术结合ActiveChassis™ 科技
为足部带来柔软的缓震效果与稳定支撑
R.Camber™ 弹性厚底特性
可在徒步时实现快速过渡
每一步的顺畅姿态，都将助你走得更远

追随寒潮降临
2023全新户外秋冬徒步系列
现已于SALOMON线上、线下门店同步上市

**2.Opt.1
标题：「她」自山野来
摘要：可隆邀你一起，见证「她」的无限可能
Opt.2
标题：@户外Girls，出发需要理由吗
摘要：穿上可隆拥抱山野，探索属于自己的热爱
Opt.3
标题：刘诗诗今日户外关键词：「她」力量
摘要：步履不停丈量天地，见证持续探索的女性力量
正文：
遵循内心，从容前行
与刘诗诗一同踏足山林野径
探索不设限的「她」力量

向上
韧意突破边界
以利落身姿，挑战探索界限
于韧力攀登中突破自我
贴合女性体型，凸显身体曲线
GORE-TEX优异防护科技，进阶防风防水
面料兼具延展性与透气性，带来舒适体感
满足高海拔攀登及徒步的硬核需求
可隆GAIA韧力冲锋衣

向远
自信奔赴山野
聆听自己的意志，升级行动力
跳脱束缚，自信迈步山林旷野
专业版型设计，游刃城市与户外
出色的STORMFORCE防水科技 ，带来强力防护
轻松应对气候变幻，助力穿林越野
唤醒活力，轻松演绎户外穿搭
可隆STORMFORCE弹力防水夹克

向前
开拓无拘新境
伴随松弛心境，自在出发向前
尽情感受山野美好，抵达无畏新境
轻盈防护面料，腋下立体剪裁
满足中高海拔登山的舒适度与实用度
多向调节风帽，减少视线遮挡
轻盈畅动防护卓越，助力轻松趣探大自然
可隆女士GORE C-KNIT防水夹克

向内
同频松弛步调
热爱探索，也享受生活
保持内心的节奏，漫步自然
触感柔软，宽松版型提升贴合体验
腰部抽绳设计，勾勒流畅美感
在松弛中享受露营的美好
可隆女士拼接梭织夹克

End
相信每一次出发
和可隆一起，发现户外的无限可能
在突破中成就更强大的自己
"""


#extra_copy_requirements = """高端品质户外生活方式品牌KOLON SPORT 可隆携手品牌代言人陈坤，于北京东方广场开启代言人官宣发布会。活动当天陈坤亲临现场，用十年的坚持行走与对自然的热爱为“生性自由”的品牌主张再添创新诠释。通过双方对自然的坚持与探索，引领大众开启高端品质的户外生活方式之旅。明星刘诗诗携手陈坤穿着可隆 GORE-TEX VOLCANO 防水棉服在寒冷环境下翻山越岭风雨无阻"""
extra_copy_requirements = " "
openai_api_key = "sk-AzNvIyV5M4bMqK1I84802e7833B849F1Be495374Db9f419c"

llm = ChatOpenAI(model="gpt-4o", temperature=1.0,api_key = openai_api_key,base_url='https://xiaoai.plus/v1')

llm_duoyang = ChatOpenAI(
    model='deepseek-chat',
    openai_api_key=deepseek_api_key,
    openai_api_base='https://api.deepseek.com/v1',
    max_tokens=100,
    temperature=1.5
)
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate.from_template(template)
product_introduction = """


作为可隆MOVE INVISIBLE徒步鞋，我们深知在户外探索中，双脚的舒适与保护至关重要。这款鞋专为跨越多种户外运动而设计，不仅外观富有层次感，更搭载了多项核心科技 。

首先，它采用了创新性的 GORE-TEX INVISIBLE科技，确保鞋子轻量、透气的同时，能有效防水，让你在湿滑泥泞的山路上也能保持脚部干爽 。其次，Vibram Mega Grip大底提供了卓越的耐用性和抓地能力，无论是崎岖山路还是湿滑泥地，都能助你稳步前行，无惧挑战 。中底的 Perfoam技术 实现了轻量化与优越回弹性，有效缓震，减轻长时间徒步带来的疲劳感，保护你的关节 。此外，鞋头特别设计的 橡胶足尖保护片 能有效防踢碰，为你的脚趾提供额外安全保障 。更有 ORTHOLITE鞋垫 带来出色的抗菌防臭功能，确保鞋内环境的持久清新与舒适 。

MOVE INVISIBLE徒步鞋，是你春日探索自然、征服户外的理想伙伴，让你在每一次迈步中，都能感受到专业科技带来的无忧体验。"""
# 定义字典
# prompt_dict = {
#      "product_name": "可隆 GORE-TEX VOLCANO 防水棉服",
#
#     "product_introduction": product_introduction,
#     "product_selling_point": """1.防风防水： 外层使用全新更具质感的锦纶材质GORE-TEX 2层面料以及全压胶工艺
# 2.轻量保暖： 内层复合PRIMALOFT保温性优异的GOLD CROSS CORE科技功能性材料填充
# 3.I.WARM 3.0加热系统： 后背可快速加热，实现快速升温
# 4.迭代升级3D立体剪裁： 并在肘部使用弹力针织拼接，更具活动性""",
#     "copy_theme": "推广主题： 暖由芯生。背后的主题故事： 严寒冬日，可隆给消费者带去双重防护保暖。外层使用全新质感防水面料，阻挡雨水侵入。内部，“芯”指保暖性优异的棉层，一键加热系统。双“芯”让能源源不断。",
#     "extra_copy_requirements":extra_copy_requirements
# }


prompt_dict = {
     "product_name": "可隆MOVE INVISIBLE 徒步鞋",

    "product_introduction": product_introduction,
    "product_selling_point": """1.防水透气技术： 采用 GORE-TEX INVISIBLE科技 
2.卓越抓地力： 采用 Vibram Mega Grip大底 
3.轻量缓震： 中底采用轻量化和回弹性优越的Perfoam技术 ，
4.保护性： 带有橡胶足尖保护片 
5.舒适与抗菌： 采用ORTHOLITE鞋垫 
""",
    "copy_theme": """春日户外探索，徒步入野。
专业科技加持，征服未知旅途。
轻量防水透气，畅享户外新境。""",
    "extra_copy_requirements":extra_copy_requirements
}





prompt = prompt.format(**prompt_dict)
print(prompt)
res = llm.invoke(prompt)
outline = res.content
OA_template = """现在你是一名文案撰写专家，首先你需要分析示例文案的风格，然后通过给定的产品名称、产品信息，产品卖点、文案主题对示例文案进行仿写创作。
#<文案要求>
1.文案每句话都需要占用单独一行，并且保持简短。
2.风格与示例文案保持一致，需要发挥创造力。
3.使用用户给定的信息，若缺少相关信息，可发挥想象力。
4.语言保持生动优美，运用情感渲染、故事叙述、形象描绘、色彩表达、情感连接等方式介绍产品。
5.正文采用分段的方法，每个段落突出不同的功能或特点，但要避免过于直白的表达。并且从感性和理性两方面阐述产品的优势。
6.无需markdown语法，输出标题、摘要、正文三部分即可。
#其他补充：{extra_copy_requirements}
</文案要求>
#<示例文案>
**1.标题：新品上市 | 和 SALOMON 溯溪鞋一起淌水入夏
摘要：穿上溯溪鞋，把夏天淌进水里
正文：初夏来袭，城市户外地图更新中
水上玩法即将解锁
季节装备，正在等待焕新……

SALOMON 溯溪鞋
助力自在踏水，一起把夏天淌进水里
专业溯溪/ TECHAMPHIBIAN 5

◎点击解锁专业淌水视角
专业实力，为涉水而生
自在探索多元场景
让户外更加安全有趣
体验这场丰富的夏季之旅

-溯溪科技-
搭载 SensiFIT™ 科技
从中底到鞋带，为双脚提供包裹与支撑
配合加固加厚鞋头
降低溯溪过程中的石块撞击风险
专业 Water Contagrip® 粘性大底
踏上湿滑地形
依旧稳定抓地，实现有效防滑

轻薄帆布拼接防碎石细孔网布
有效加速水分蒸发，实现快速干燥
同时防止水中泥土掉落鞋内

快乐踩水/ TECHSONIC
皮质材料打造摩登之感
透气设计为凉鞋开辟全新领域
户外溯溪or城市踏水，舒适随时相伴
-踏水守护-
采用 quickLACE 快速系带系统
在短时间内迅速完成系带动作
搭配SensiFIT™设计
有效减轻脚面压力，自在畅快踩水

可折叠鞋跟设计
满足户外和城市穿着的双重需求
场景转变，随时应变

夏季地图更新完成
户外水域成功解锁
穿上 SALOMON 溯溪鞋款
一起淌水出发，迎接初夏到来
全国门店现已上市
**2.标题：超弹STORMFORCE，刘诗诗先替你们穿了
摘要：自在的户外人，先享受春天
正文：复苏的气息悄然蔓延
春意早有迹可循
唤醒探索的热情
先「型」游走早春


刘诗诗的入春计划
轻装上阵
无拘无束自成风景
唤醒户外野生能量
入山感知早春生机

陈坤的入春计划
迈步登高
自在探赏山林秘境
大胆踏步登高远眺
畅行随心自在无拘


万物赋新，心向旷野
感受户外自然力
一起开启入春计划

自然同频，晴雨无阻
邀约志同道合的好友
玩转山林间的缤纷野趣
Storm Force 3层科技面料，防风防水高弹透汽
全方位守护，让组队快乐加倍



穿行春日，随心而动
轻步赴春光
让身体沐浴自然能量
腋下连片式立体剪裁
专业版型兼具灵活与实用
尽情释放探索天性


迎风而上，生机在线
乘着风，追逐天地
探索每一处未知的角落
渐变色印花款式，时尚美观
唤醒好心情，尽显活力轻盈

可隆入春计划已安排
带上蠢蠢欲动的探索心
一起迈入蓬勃自然

可隆入春计划已安排
带上蠢蠢欲动的探索心
一起迈入蓬勃自然
**3.标题：唤山者系列 | 拨开云雾，遇见墨色括苍
摘要：水墨山峦，尽写诗意
正文：眼前雾气盘绕
是山脉有形的呼吸

◎拨开云雾——
从中国户外名山汲取力量和灵感
打造Salomon首个本土特别企划
「唤山者」系列重磅压轴款
XT-QUEST括苍山
以中式水墨美学，勾勒临海括苍山磅礴之势
双脚踏访云端，传递山的呼唤

越上山巅 纵览诗意
作为Salomon赞助的 “柴古唐斯越野赛”的起源地
临海括苍山一直是越野跑者心中的“白月光”
也是Salomon首个中国限定「唤山者」系列的收官压轴之山

括苍临海，空蒙缥缈
漫步云端的诗境感，在中式水墨美学的步履间得到完整诠释
黑白交融犹如墨色入水
于鞋面肆意挥洒，诠释括苍山云雾缥缈的画中诗意

为致敬柴古唐斯2023年新赛季
XT-QUEST括苍山 作为「唤山者」压轴鞋款
将于10月28日线下同步发售

笔触细腻 勾勒自然
鞋款整体黑白交叠，似一幅水墨丹青图卷
诗意般诠释着括苍山自然风貌
虚实相交，传递山之呼唤
行进山中，感受在云雾间攀登的呼吸感

以传统水墨装饰鞋盒外观
渐变色调与云雾缭绕的括苍山遥相呼应
山气氤氲，似将倾盒而出

科技加持 惬意探山
搭载专业登山徒步鞋专用的4D ADVANCED CHASSIS™科技大底
借助其动态支撑与缓冲性能
为运动过程中的每一步，提供稳定可靠的过渡

SALOMON XT-QUEST括苍山
10月20日-10月27日
SALOMON官方小程序抽签通道开启
即刻前往，感悟括苍诗意
</示例文案>
"
#公众号产品
Product_message_OA_prompt: "
#<产品名称>{product_name}</产品名称>
#<产品信息>{product_introduction}</产品信息>
#<产品卖点>{product_selling_point}</产品卖点>
#<文案主题>{copy_theme}</文案主题>
#<文案指导>{outline}</文案指导>
#产品受众：{product_audience}"""
oa_prompt = ChatPromptTemplate.from_template(OA_template)
#extra_copy_requirements ="""<产品介绍>主题： XT-6 春季配色推文 (需要有个春季新奇探索的主题)\\n\\n内容：\\n整体围绕XT-6 主概念“新奇探索奇观” （寓意身穿XT-6 带你感受身边的有趣奇观，所有XT-6 素材的拍摄都是以户外/城市景色但以有趣的镜头语言来表达XT-6  ）并带出春季配色系列（一共6个配色）\\n\\n1.XT-6 三款颜色\\nXT-6 黑色 （模特图+白底图）\\nXT-6 灰色（模特图+白底图）\\nXT-6 灰褐色（模特图+白底图）\\n2.XT-6 FT 一款颜色\\nXT-6 复古卡其色 （模特图+白底图）\\nXT-6 深邃蓝 （只有白底图）\\nFT （Future Tech）致力于寻找现代科技感材质来赋予经典鞋款XT-6全新的生命力\\n采用Sensifit 和 Endofit的组合，带来恰到好处的贴合感，单层网眼和3D 开放网面保证透气性的同时增强了产品的机能感。\\n3.XT-6 Mindful 两款颜色\\nXT-6 Mindful 香草色（模特图+白底图）\\nXT-6 Mindful 灰褐色（模特图+白底图）\\n萨洛蒙从可持续环保概念出发，打造为环保而生的Mindful系列，聚焦于气候变化问题。因此鞋面采用至少30%以上混合可回收材质打造而成包括再生聚酯和TPU碎片的混合物。</产品介绍>"""
oa_prompt_dict = {
     "product_name": "可隆MOVE INVISIBLE 徒步鞋",

    "product_introduction": product_introduction,
    "product_selling_point": """1.防水透气技术： 采用 GORE-TEX INVISIBLE科技 
2.卓越抓地力： 采用 Vibram Mega Grip大底 
3.轻量缓震： 中底采用轻量化和回弹性优越的Perfoam技术 ，
4.保护性： 带有橡胶足尖保护片 
5.舒适与抗菌： 采用ORTHOLITE鞋垫 
""",
    "copy_theme": """春日户外探索，徒步入野。
专业科技加持，征服未知旅途。
轻量防水透气，畅享户外新境。""",
    "outline": outline,
    "product_audience":"""户外运动爱好者： 追求专业性能、舒适体验和全地形适应能力的徒步、登山爱好者 。
注重科技与品质的消费者： 对GORE-TEX INVISIBLE、Vibram Mega Grip、Perfoam和ORTHOLITE等技术有认知或追求的消费者 。
日常休闲户外需求者： 寻求在日常户外活动中也能穿着舒适、兼具设计感的鞋履。""",
    "extra_copy_requirements":extra_copy_requirements

}
oa_prompt = oa_prompt.format(**oa_prompt_dict)
print(oa_prompt)
res = llm.invoke(oa_prompt)
print(res.content)


