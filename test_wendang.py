import asyncio
import subprocess
from pathlib import Path
from langchain_community.document_loaders import (
    UnstructuredPDFLoader,
    UnstructuredWordDocumentLoader,
    UnstructuredPowerPointLoader,
)


class LangchainDocumentExtractor:
    """
    一个使用 Langchain 和其他工具从不同类型文件中异步提取文本的工具类。
    """

    async def _load_and_extract(self, loader):
        """
        一个内部辅助函数，用于加载并提取文本。
        在实际应用中，对于大型文件，建议使用 loop.run_in_executor 来避免阻塞。
        在此示例中，为简化起见，直接调用。
        """
        # 使用 asyncio.to_thread 将同步的 loader.load() 操作变为非阻塞
        documents = await asyncio.to_thread(loader.load)
        full_text = [doc.page_content for doc in documents]
        return "\n".join(full_text)

    async def extract_text_from_doc_antiword(self, file_path):
        """
        使用 antiword 异步提取 DOC (.doc) 文档的文本。
        确保 'antiword' 命令在您的系统 PATH 中可用。
        """
        print(f"正在尝试使用 antiword 提取 DOC 文件: {file_path}")
        try:
            # 使用 asyncio.create_subprocess_exec 来异步运行命令
            process = await asyncio.create_subprocess_exec(
                'antiword', str(file_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                return stdout.decode('utf-8', errors='ignore')
            else:
                error_message = stderr.decode('utf-8', errors='ignore').strip()
                print(f"使用 antiword 处理 {file_path} 时出错: {error_message}")
                # 提供更具体的错误信息
                if "not a Word Document" in error_message:
                    return "错误：文件不是一个有效的 Word 文档。"
                if "command not found" in error_message.lower() or process.returncode == 127:
                    return "错误：'antiword' 命令未找到。请确保它已安装并位于系统的 PATH 中。"
                return f"antiword 执行失败，返回码: {process.returncode}"
        except FileNotFoundError:
            print("错误: 'antiword' 命令未找到。请确保它已安装并位于系统的 PATH 中。")
            return "错误：'antiword' 命令未找到。请确保它已安装并位于系统的 PATH 中。"
        except Exception as e:
            print(f"处理 {file_path} 时发生未知错误: {str(e)}")
            return f"提取文本时发生未知错误: {str(e)}"

    async def extract_text(self, file_path: str):
        """
        根据文件扩展名异步提取文本。
        支持 .pdf, .docx, .pptx, 和 .doc 文件。
        """
        if not Path(file_path).is_file():
            message = f"错误：文件不存在于路径 {file_path}"
            print(message)
            return message

        file_path_obj = Path(file_path)
        extension = file_path_obj.suffix.lower()
        loader = None

        if extension == ".pdf":
            loader = UnstructuredPDFLoader(file_path)
            print(f"正在使用 UnstructuredPDFLoader 提取 PDF (.pdf) 文件: {file_path}")
        elif extension == ".docx":
            loader = UnstructuredWordDocumentLoader(file_path)
            print(f"正在使用 UnstructuredWordDocumentLoader 提取 Word (.docx) 文件: {file_path}")
        elif extension == ".pptx":
            loader = UnstructuredPowerPointLoader(file_path)
            print(f"正在使用 UnstructuredPowerPointLoader 提取 PowerPoint (.pptx) 文件: {file_path}")
        elif extension == ".doc":
            # 对于 .doc 文件，我们使用 antiword
            return await self.extract_text_from_doc_antiword(file_path)
        else:
            message = f"不支持的文件类型: {file_path}。目前支持 .pdf、.docx、.pptx 和 .doc。"
            print(message)
            return message

        if loader:
            return await self._load_and_extract(loader)
        return None


async def main():
    """
    主函数，用于演示 LangchainDocumentExtractor 的使用。
    """
    extractor = LangchainDocumentExtractor()

    # --- 将下面的路径替换为你的文件路径 ---
    # 注意：你需要创建一个名为 'test_files' 的文件夹，并将相应的文件放入其中。
    test_files_dir = Path("test_files")
    test_files_dir.mkdir(exist_ok=True)

    # 创建一些虚拟文件用于测试，如果它们不存在的话
    # 在实际使用中，请替换为您的真实文件路径
    doc_file_path = test_files_dir / "sample.doc"
    docx_file_path = test_files_dir / "sample.docx"
    pdf_file_path = test_files_dir / "sample.pdf"
    unsupported_file_path = test_files_dir / "unsupported.txt"

    # 提示用户
    print("=" * 50)
    print("请确保 'test_files' 文件夹中有名为 'sample.doc' 的文件。")
    print("您还需要安装 'python-docx', 'pypdf', 'unstructured', 'langchain-community' 和 'antiword'。")
    print("使用 pip install python-docx pypdf unstructured langchain-community 进行安装。")
    print("Antiword 需要通过您的系统包管理器安装 (例如: sudo apt-get install antiword)。")
    print("=" * 50 + "\n")

    # --- 1. 提取 .doc 文件 ---
    # 请将一个 .doc 文件放在 test_files/sample.doc
    print("\n--- 1. 提取 DOC (.doc) 文本 ---")
    if doc_file_path.exists():
        extracted_doc_text = await extractor.extract_text(str(doc_file_path))
        if extracted_doc_text:
            print(f"从 {doc_file_path} 提取的文本 (前500字符):")
            print(extracted_doc_text[:500])
        else:
            print(f"{doc_file_path} 文本提取失败。")
    else:
        print(f"跳过 .doc 测试，文件不存在: {doc_file_path}")

    # # --- 2. 提取 .docx 文件 ---
    # # 请将一个 .docx 文件放在 test_files/sample.docx
    # print("\n--- 2. 提取 DOCX (.docx) 文本 ---")
    # if docx_file_path.exists():
    #     extracted_docx_text = await extractor.extract_text(str(docx_file_path))
    #     if extracted_docx_text:
    #         print(f"从 {docx_file_path} 提取的文本 (前500字符):")
    #         print(extracted_docx_text[:500])
    #     else:
    #         print(f"{docx_file_path} 文本提取失败。")
    # else:
    #     print(f"跳过 .docx 测试，文件不存在: {docx_file_path}")
    #
    # # --- 3. 提取 .pdf 文件 ---
    # # 请将一个 .pdf 文件放在 test_files/sample.pdf
    # print("\n--- 3. 提取 PDF (.pdf) 文本 ---")
    # if pdf_file_path.exists():
    #     extracted_pdf_text = await extractor.extract_text(str(pdf_file_path))
    #     if extracted_pdf_text:
    #         print(f"从 {pdf_file_path} 提取的文本 (前500字符):")
    #         print(extracted_pdf_text[:500])
    #     else:
    #         print(f"{pdf_file_path} 文本提取失败。")
    # else:
    #     print(f"跳过 .pdf 测试，文件不存在: {pdf_file_path}")
    #
    # # --- 4. 尝试提取不支持的文件类型 ---
    # print("\n--- 4. 尝试提取不支持的文件类型 ---")
    # if not unsupported_file_path.exists():
    #     unsupported_file_path.touch()  # 创建一个空的 txt 文件
    #
    # await extractor.extract_text(str(unsupported_file_path))


if __name__ == "__main__":
    # 运行主异步函数
    asyncio.run(main())