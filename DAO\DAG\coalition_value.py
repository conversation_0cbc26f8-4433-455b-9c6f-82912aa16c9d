import random
import itertools


def generate_coalition_values(players):
    """
    根据给定的玩家生成随机联盟值

    Args:
        players: 玩家列表

    Returns:
        一个字典，包含联盟及其对应的随机值
    """
    coalition_values = {}

    # 生成所有可能的联盟（大小至少为1的联盟）
    for size in range(1, len(players) + 1):
        for coalition in itertools.combinations(players, size):
            # 随机生成一个值，范围可以根据需要调整
            value = random.randint(0, 20)  # 这里的范围是0到20
            coalition_values[coalition] = value

    # 生成一些特定的联盟值（可以根据需要自定义）
    for coalition in itertools.combinations(players, 2):
        coalition_values[coalition] = random.randint(5, 15)  # 2人联盟的值

    # 生成大联盟的值
    grand_coalition = tuple(sorted(players))
    coalition_values[grand_coalition] = random.randint(10, 30)  # 大联盟的值

    # 移除空集
    coalition_values.pop(tuple(), None)

    return coalition_values


# 示例用法
players = ['a', 'b', 'c', 'd']
coalition_values = generate_coalition_values(players)

# 格式化输出，确保所有单个元素都以元组形式表示
formatted_values = {tuple(coalition) if len(coalition) > 1 else (coalition[0],): value for coalition, value in coalition_values.items()}

# 打印联盟值
print(formatted_values)

# 计算并打印联盟的数量
total_coalitions = len(formatted_values)
print(f"总共有 {total_coalitions} 个联盟。")