from IPython.utils import io
from serpapi import GoogleSearch
import os
import string
import re


import openai
import urllib.request, json

from langchain_openai import ChatOpenAI
from serpapi import GoogleSearch
from dotenv import load_dotenv
from IPython.utils import io
# from Multimodal_gemini import ImageAnalyzer



load_dotenv(r'D:\硕士\aiagent\gpt_CWSC\.env',override=True) # 关键：`override=True`

openai_api_key = os.getenv("OPENAI_API_KEY")
deepseek_api_key = os.getenv("DEEPSEEK_API_KEY_2")
print(deepseek_api_key)
print(openai.api_key)


template = """你是CHAUMET品牌的小红书文案编辑专家，请按照以下思维模式进行文案创作：。
## 根据提供的信息：产品名称是{Product_Information}、产品的受众人群是{Product_Audience}、产品的核心卖点(也是最重要的一点)是{Product_Selling_Points}以及文案主题是{Copy_Theme}，充分理解并分析品牌的调性、产品的特点和卖点，为{Product_Information}生成一条标题和相应的正文文案。
#产品补充背景
{Additional_Resource}
##语言风格：
- 必须仔细分析示例中的语言风格，包括语调（优雅、高端）、情感表达（浪漫、温馨）、用词偏好（精准、富有画面感），并据此创作与品牌调性高度一致的文案。
##文案生成流程
1.分析材料：理解产品信息与目标受众。
2.提炼卖点：从产品资料中提取独特优势，明确目标受众需求，尤其强调“全新”、“迷你”、“玲珑小巧”等特点。
3.标题创作：以简洁有力的短句或互动性词句呈现，吸引注意。标题使用高大上或情感化的表达，营造氛围感，增强吸引力，可考虑融入季节或氛围感词语，并直接点明产品系列或核心特点。
4.撰写正文：正文少于五句话，切记每句话一定不要太长，一定要精炼文字，直接点出产品特点和价值， 保持品牌高端调性，并适度运用表情符号。
5.校对优化：确保文案符合所有要求，进行必要的修改
#示例
###示例1：
- 标题：尚美蜂巢BEE MY LOVE ｜大胆设计，”蜂”芒跃现
CHAUMET BEE MY LOVE 爱·巢系列
大胆设计，重塑标志性蜂巢图案 ，
白金与玫瑰金的个性叠戴，
尚美蜂巢戒指与手镯的自由组合，
摩登态度跃然身畔。
###示例2：
- 标题：风雅约瑟芬｜创造叠戴法则
- 正文 ：
以多元搭配打破风格禁锢，
叠戴Joséphine约瑟芬皇后系列钻戒，张扬鲜明个性。
一眼可辨的“V”形设计彼此相契，
轻缀指间，优雅与大胆一举兼得。
"""









llm_gpt4o = ChatOpenAI(model="gpt-4o", temperature=1.0,api_key = openai_api_key,base_url='https://xiaoai.plus/v1',top_p=0.7)

llm_deepseek = ChatOpenAI(
    model='deepseek-chat',
    openai_api_key=deepseek_api_key,
    openai_api_base='https://api.deepseek.com/v1',
    max_tokens=100,
    temperature=1.5
)
from langchain_core.prompts import ChatPromptTemplate

prompt = ChatPromptTemplate.from_template(template)
# 定义字典
prompt_dict = {
    "Product_Information": "Bee de Chaumet 系列项链",
    "Product_Audience": "追求独特设计、精致工艺和寓意深远珠宝的优雅女性。",
    "Product_Selling_Points": "黄金蜜蜂造型，明亮式切割钻石点睛，精湛工艺，独特寓意（勤劳、忠诚、皇室）。",
    "Copy_Theme": "自然灵感、奢华雅致、精湛工艺、独特寓意",

}

# analyzer = ImageAnalyzer(
#         env_path=r'D:\硕士\aiagent\gpt_CWSC\.env',
#         model_name="gemini-2.0-flash",
#         temperature=0,
#         timeout=30
#     )
#
# print("\n" + "=" * 50)


# prompt1 = "使用中文描述这些图片的{}产品，字数大概300左右".format(prompt_dict['Product_Information'])
#
# print("\n=== 示例4: 分析多张本地图片（简化列表） ===")
# local_images = [
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\1.png",
#                 r"D:\硕士\aiagent\gpt_CWSC\pictures\2.png"  # 假设存在
#             ]
#
# result = analyzer.analyze_image(local_images, prompt=prompt1,is_local= True)
# if result["success"]:
#     print("分析结果:", result["result"])
#     print(f"处理图片数: {result['processed_images_count']}/{result['total_images_count']}")
# else:
#     print("分析失败:", result["error"])






#additional_data2 =  result["result"]
additional_data2 = """这是一款来自 Bee de Chaumet 系列的精致项链，以其独特的蜜蜂造型吸引目光 。项链的主体是一只栩栩如生的黄金蜜蜂，其设计巧妙，细节之处彰显匠心 。蜜蜂的造型由黄金精妙打造，呈现出圆润而富有层次感的线条，仿佛蜜蜂在空中轻盈飞舞 。


项链的亮点在于蜜蜂的眼睛部分，镶嵌着明亮式切割钻石 。这些钻石不仅为蜜蜂增添了一丝灵动与光彩，更在黄金的衬托下显得熠熠生辉，使得整个设计更加生动传神 。蜜蜂的翅膀设计也十分考究，可能采用了抛光或拉丝工艺，以营造出不同的质感和光影效果，使其看起来更具立体感 。


除了蜜蜂本身，项链的链条也与蜜蜂造型完美融合，细致的黄金链条与主体相得益彰，既保证了佩戴的舒适性，又突出了蜜蜂的精致造型 。项链的整体设计传达出 Chaumet 品牌对自然生灵的赞美和精湛的珠宝工艺。蜜蜂在 Chaumet 品牌中也具有特殊的象征意义，代表着勤劳、忠诚和皇室的庇护，赋予了这款项链更深层次的寓意。

这款 Bee de Chaumet 项链不仅是一件珠宝，更是一件艺术品，完美融合了自然元素与奢华材质，适合日常佩戴或作为特殊场合的点睛之笔，展现佩戴者独特的品味与优雅气质。"""
print(additional_data2)
prompt_dict['Additional_Resource'] = additional_data2

# 使用字典填充


text1 = """
角色：一名产品受众人群分析师。

指令：根据补充资料细化人类给出的的受众人群，字数大概在20字左右,只需返回最重要的结果即可，优化后的结果以双引号输出。

补充资料：{additional_data}

"""
#
Product_Audience_prompt = ChatPromptTemplate(
    [
        (
            "system",
            text1
        ),
        ("human", "{input}"),
    ]
)
prompt_dict['Product_Audience'] =llm_deepseek.invoke(Product_Audience_prompt.format(additional_data = additional_data2,input=prompt_dict['Product_Audience'])).content

prompt = prompt.format(**prompt_dict)
print(prompt)
res = llm_gpt4o.invoke(prompt)
print(res.content)


