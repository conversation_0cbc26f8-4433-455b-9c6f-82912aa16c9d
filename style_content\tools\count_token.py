import re

import tiktoken


def num_tokens_from_messages(messages, model="gpt-3.5-turbo-0125"):
    """Return the number of tokens used by a list of messages."""
    try:
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        print("Warning: model not found. Using cl100k_base encoding.")
        encoding = tiktoken.get_encoding("cl100k_base")
    if model in {
        "gpt-3.5-turbo-0125",
        "gpt-3.5-turbo",
        "gpt-4-turbo-preview",
        "gpt-4-0125-preview",
        "deepseek-chat",
        "gpt-4o",
        "gpt-4o-mini",
        }:
        tokens_per_message = 3
        tokens_per_name = 1
    else:
        raise NotImplementedError(
            f"""num_tokens_from_messages() is not implemented for model {model}. See https://github.com/openai/openai-python/blob/main/chatml.md for information on how messages are converted to tokens."""
        )
    num_tokens = 0
    if model == "deepseek-chat":
        for message in messages:
            for key, value in message.items():
                num_tokens += len(value) * 0.6 - len(re.findall(r'[A-Za-z]', value)) * 0.3
    else:
        for message in messages:
            num_tokens += tokens_per_message
            for key, value in message.items():
                num_tokens += len(encoding.encode(value))
                if key == "name":
                    num_tokens += tokens_per_name
        num_tokens += 3  # every reply is primed with <|start|>assistant<|message|>
    return num_tokens


# from openai import OpenAI
# import os
#
#
#
# example_messages = [
#     {"role": "system",
#      "content": "You are a helpful assistant."},
#     {"role": "user",
#      "content": f'''现在你是一名文案撰写专家，首先你需要分析示例文案的风格，然后通过给定的文案信息对其进行仿写。
#                 <文案要求>
#                 1.文案每句话都需要占用单独一行。
#                 2.风格与示例文案保持一致，发挥创造力。
#                 3.使用用户给定的文案信息,若用户没指定信息，则发挥自己想象力。
#                 4.语言保持生动优美，运用情感渲染、故事叙述、形象描绘、色彩表达、情感连接等方式介绍产品。
#                 </文案要求>
#                 '''}, ]
#
# for model in [
#     # "gpt-3.5-turbo-0125",
#     # "gpt-3.5-turbo",
#     # "gpt-4-turbo-preview",
#     # "gpt-4-0125-preview",
#     # "deepseek-chat",
#     "gpt-4o-mini",
#     ]:
#     print(model)
#     # example token count from the function defined above
#     print(f"{num_tokens_from_messages(example_messages, model)} prompt tokens counted by num_tokens_from_messages().")
#     # example token count from the OpenAI API
#     if model != "deepseek-chat":
#         client = OpenAI(api_key="********************************************************")
#         response = client.chat.completions.create(model=model,
#                                                   messages=example_messages,
#                                                   temperature=0, )
#         messages = [{"role": "system", "content": response.choices[0].message.content}]
#     else:
#         client = OpenAI(api_key="sk-432a28db77fe43d288368f3525db2742",base_url="https://api.deepseek.com/")
#         response = client.chat.completions.create(model=model,
#                                                   messages=example_messages,
#                                                   temperature=0, )
#         messages = [{"role": "system", "content": response.choices[0].message.content}]
#
#     print(f'{response.usage.prompt_tokens} prompt tokens counted by the OpenAI API.')
#     print(f"{num_tokens_from_messages(messages, model)-7} completion tokens counted by num_tokens_from_messages().")
#     print(f'{response.usage.completion_tokens} completion tokens counted by the OpenAI API.')
#     print()