# Generated by Django 5.0.1 on 2024-04-02 12:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Usage',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('use_time', models.DateField()),
                ('token_num', models.IntegerField()),
                ('userid', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddConstraint(
            model_name='usage',
            constraint=models.UniqueConstraint(fields=('userid', 'use_time'), name='unique_userid_time'),
        ),
    ]
