import pandas as pd
import re
import os
from datetime import datetime, timedelta
import os  # 新增导入os模块

from common_tools.Use_email import EmailSender


class ExcelReader:
    # 定义文件路径为类级别变量（作为实例的全局变量）
    # 使用原始字符串处理反斜杠，确保路径正确
    DEFAULT_FILE_PATH = r'E:\BaiduNetdiskDownload\英语\AI训练\AI for可隆\PINSIST明星生日需求.xlsx'

    def __init__(self, file_path=None):
        """
        初始化ExcelReader，指定文件路径。
        如果未提供file_path，则默认为DEFAULT_FILE_PATH。
        """
        self.file_path = file_path if file_path is not None else self.DEFAULT_FILE_PATH
        self.df = None  # 用于存储读取的DataFrame

    def read_excel_data(self):
        """
        将Excel文件读取到pandas DataFrame中。
        处理FileNotFoundError、ImportError和其他异常。
        如果成功，返回DataFrame；否则返回None。
        """
        try:
            # 使用pd.read_excel()读取.xlsx文件，并指定引擎
            self.df = pd.read_excel(self.file_path, engine='openpyxl')
            print(f"成功从文件读取数据: {self.file_path}")
            return self.df
        except FileNotFoundError:
            print(f"错误: 未找到文件 '{self.file_path}'。请确保文件路径正确。")
            return None
        except ImportError:
            print("错误: 未安装'openpyxl'库。请使用 'pip install openpyxl' 安装。")
            return None
        except Exception as e:
            print(f"读取文件时发生错误: {e}")
            return None



    def calculate_pre_date(self, birthday_str, time_extract_str):
        """
        根据生日和提取时间字符串计算提前日期。
        time_extract_str 示例: "提前3周&" 或 "提前一周&"
        birthday_str 支持的格式: 'YYYY-MM-DD', 'MM-DD', 'M.D' (如 3.1 表示3月1日)
        """
        try:
            # 检查输入值
            if pd.isna(birthday_str):
                print("生日数据为空")
                return None
                
            if pd.isna(time_extract_str):

                print("提取时间数据为空,按照默认值2周处理")
                time_extract_str = "提前2周&"

            
            # 确保time_extract_str是字符串
            if not isinstance(time_extract_str, str):
                time_extract_str = str(time_extract_str)
                
            # 将生日字符串转换为 datetime 对象
            current_year = datetime.now().year
            
            # 处理不同的日期格式
            if isinstance(birthday_str, float) or isinstance(birthday_str, int):
                # 如果是数值类型，尝试转为字符串
                birthday_str = str(birthday_str)
            
            # 格式: M.D (如 3.1)
            if '.' in birthday_str:
                try:
                    month, day = map(int, birthday_str.split('.'))
                    birthday = datetime(current_year, month, day)
                    print(f"解析生日为: {birthday} (解析'月.日'格式: {birthday_str})")
                except (ValueError, AttributeError) as e:
                    print(f"无法解析'月.日'格式: {birthday_str}, 错误: {e}")
                    return None
            # 格式: MM-DD    
            elif len(birthday_str) == 5 and birthday_str[2] == '-':
                try:
                    birthday = datetime.strptime(f"{current_year}-{birthday_str}", "%Y-%m-%d")
                    print(f"解析生日为: {birthday} (解析'MM-DD'格式: {birthday_str})")
                except ValueError as e:
                    print(f"无法解析'MM-DD'格式: {birthday_str}, 错误: {e}")
                    return None
            # 格式: YYYY-MM-DD
            else:
                try:
                    birthday = datetime.strptime(birthday_str, "%Y-%m-%d")
                    print(f"解析生日为: {birthday} (解析'YYYY-MM-DD'格式: {birthday_str})")
                except ValueError as e:
                    print(f"无法解析'YYYY-MM-DD'格式: {birthday_str}, 错误: {e}")
                    return None

            # 使用正则表达式提取周数
            match = re.search(r'(?<=提前).*?(?=周)', time_extract_str)
            if match:
                weeks_str = match.group(0)
                # 处理中文数字转换为阿拉伯数字
                chinese_to_arabic = {
                    '零': 0,'一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
                    '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,'两':2,
                    '1': 1, '2': 2, '3': 3, '4': 4, '5': 5,'0': 0,
                    '6': 6, '7': 7, '8': 8, '9': 9, '10': 10
                }
                
                if weeks_str in chinese_to_arabic:
                    weeks = chinese_to_arabic[weeks_str]
                    print(f"成功将中文数字 '{weeks_str}' 转换为 {weeks}")
                else:
                    try:
                        weeks = int(weeks_str)
                        print(f"将字符串 '{weeks_str}' 转换为数字 {weeks}")
                    except ValueError:
                        print(f"无法将 '{weeks_str}' 转换为数字")
                        return None
            else:
                print(f"未能在时间提取字符串中找到周数: {time_extract_str}")
                return None

            # 修改后的代码
            pre_date = (birthday - timedelta(weeks=weeks)).replace(hour=8, minute=0, second=0, microsecond=0)

            print(f"提醒日期和时间是: {pre_date}")

            # 返回月份-天的日期数据
            return pre_date.strftime("%m-%d")

        except ValueError as ve:
            print(f"日期格式错误或无法解析: {ve}")
            return None
        except Exception as e:
            print(f"计算提前日期时发生错误: {e}")
            return None


    def Use_email(self, recipient, subject, content):
        """
        使用工具发送邮件。
        :param recipient: 收件人邮箱地址（字符串或列表）。
        :param subject: 邮件主题。
        :param content: 邮件正文。
        """
        print("--- QQ 邮箱发送示例 ---")
        qq_sender = EmailSender(
            sender_email="<EMAIL>",
            sender_password="iknyosisazkhdjdh",  # 替换为你的QQ邮箱授权码
            smtp_server="smtp.qq.com",
            smtp_port=587
        )

        # 单个收件人
        result_qq_single = qq_sender.send_email(
            recipient="<EMAIL>",
            subject="来自 Python 类的问候",
            content="你好，这是一封通过 EmailSender 类发送的测试邮件！"
        )





# 示例用法:
if __name__ == "__main__":
    # 使用默认路径创建ExcelReader类的实例
    reader = ExcelReader()
    df = reader.read_excel_data()

    if df is not None:
        # 打印列名，便于确认
        print("数据框中的列名:", df.columns.tolist())
        print("数据框中的前几行示例:")
        print(df.head())
        print(df.shape)  # 打印数据框的形状（行数和列数）
        
        # 添加一个新列"提前日期"
        birthday_col = '生日'  # 根据实际Excel列名修改
        time_extract_col = '提醒时间'  # 根据实际Excel列名修改
        
        # 确认这些列是否存在
        if birthday_col not in df.columns:
            print(f"警告: '{birthday_col}'列在数据框中不存在")
            print("请从以下列名中选择正确的生日列名:", df.columns.tolist())
        
        if time_extract_col not in df.columns:
            print(f"警告: '{time_extract_col}'列在数据框中不存在")
            print("请从以下列名中选择正确的提醒时间列名:", df.columns.tolist())
            
        # 只有当这些列都存在时才继续
        if birthday_col in df.columns and time_extract_col in df.columns:
            # 遍历DataFrame的每一行，为每行计算提前日期
            df['提前日期'] = df.apply(
                lambda row: reader.calculate_pre_date(
                    row[birthday_col], row[time_extract_col]
                ),
                axis=1
            )
            
            # 获取今天的日期（月-日格式）
            today = datetime.now().strftime("%m-%d")
            print(f"今天的日期是: {today}")
            
            # 添加一个新列"是今天"，判断提前日期是否等于今天
            df['是否提醒'] = df['提前日期'] == today
            
            # 输出今天需要提醒的条目
            today_reminders = df[df['是否提醒'] == True]
            if not today_reminders.empty:
                print("\n今天需要提醒的明星生日:")
                print(today_reminders[['明星', '生日', '提前日期']])
                email_list = today_reminders['提醒account'].str.split('\n')



            #     reader.Use_email(today_reminders['提醒account'], "生日提醒", "今天有生日的明星:" + ', '.join(today_reminders['明星'].tolist()))
            # else:
            #     print("\n今天没有需要提醒的明星生日")


        


