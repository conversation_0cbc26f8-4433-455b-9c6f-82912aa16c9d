from django.db import models
from django.contrib.auth.models import User

# Create your models here.
class Fileinfo(models.Model):
    id = models.AutoField(primary_key=True)
    document_name = models.CharField(max_length=100)
    size = models.IntegerField()
    store_path = models.CharField(max_length=100)
    upload_user = models.ForeignKey(User, on_delete=models.CASCADE)
    create_time = models.DateTimeField(auto_now_add=True)