{% load static %}
<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文案检查</title>

    <style>
        /*页面的容器元素添加顶部的外边距*/
        .container {
            max-width: 90%;
            margin-top: 15px;
            background: #fff;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
            /* 水平居中 */
            margin-right: auto;
            /* 水平居中 */
            margin-left: auto;
            position: relative;
        }

        /* 表单中的每个输入项提供底部外边距 */
        .form-group {
            margin-bottom: 15px;
            font-size: 22px;
        }

        /* 检查结果部分 */
        #checkResult {
            margin-top: 20px;
            display: flex;
            flex-direction: column;

        }

        /* 检查结果区域的样式 */
        #resultContent {
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
        }


        /* 定义了加载指示器的样式 */
        .loading-indicator {
            display: none;
            /* 默认隐藏 */
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-top: 8px;
            /* position: absolute; */
            /* 绝对定位 */
            /* top: 70%; */
            /* 垂直居中 */
            /* left: 25%; */
            /* 水平居中 */
            /* transform: translate(-50%, -50%); */
            /* 完全居中 */
            z-index: 10;
            /* 确保加载指示器位于页面上方 */
        }

        .loading-indicator img {
            width: 25px;
            animation: spin 1s linear infinite;
            /* 添加旋转动画 */
        }

        /* 旋转动画 */
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 复制按钮样式 */
        .copy-button {
            margin-top: 0px;
            padding: 5px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;

        }

        .copy-button:hover {
            background-color: #0056b3;
        }

        /* 控制文案输入区域的样式 */
        #copywritingContent,
        #referenceContent {
            min-height: 110px;
            /* 设置最小高度 */
            max-height: 500px;
            /* 设置最大高度 */
            overflow-y: auto;
            /* 超出内容时显示垂直滚动条 */
            resize: vertical;
            /* 允许用户垂直调整大小 */
            padding: 10px;
            width: 100%;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 10px;
        }
    </style>
</head>

<body>
    <div class="container">

        <form id="checkForm">
            <div class="form-group">
                <label for="copywritingContent"
                    style="display: block; text-align: center;background-color: #f8f8f8; width: 100%; box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.15);">文案内容</label>
                <textarea class=" form-control" id="copywritingContent" placeholder="在此粘贴您的文案"
                    style="background-color: #f9f9f9;"></textarea>
                <button type="button" class="btn btn-secondary "
                    style="margin-top: -4px;float: right;margin-right: 38px;"
                    onclick="clearContent('copywritingContent')">清空内容</button>
            </div>
            <div class="form-group">
                <label for="referenceContent"
                    style="margin-top: 55px;display: block; text-align: center;background-color: #f8f8f8; width: 100%;box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.15);">参考资料</label>
                <textarea class="form-control" id="referenceContent" placeholder="在此粘贴相关参考资料"
                    style="background-color: #f9f9f9;"></textarea>
                <button type="button" class="btn btn-primary" id="generate" onclick="submitCheck()"
                    style="background-color: #5C6D77; border-color: #5C6D77;">提交检查</button>
                <button type="button" class="btn btn-secondary"
                    style="margin-top: -4px;float: right;margin-right: 15px; "
                    onclick="clearContent('referenceContent')">清空资料内容</button>
            </div>



        </form>
        <div id=" checkResult" style="margin-top: 10px;">
            <h3
                style="font-size: 28px;display: block; text-align: center;margin-bottom: 9px;text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);">
                检查反馈</h3>
            <!-- 加载指示器 -->
            <div id="loadingIndicator" class="loading-indicator">
                <img src="{% static 'pic/loading.png' %}" alt="Loading.." />
                <p style="font-size: 15px;">正在检查文案，请稍候...</p>
            </div>

            <div id="resultContent" class="alert alert-info">
                <!-- 结果将展示在这里 -->
            </div>
        </div>
        <!-- 复制按钮 -->
        <!-- <button type="button" class="btn btn-primary" id="copyButton" onclick="copyResult()"
            style="background-color: #5C6D77; border-color: #5C6D77;margin-left: 970px;">复制结果</button> -->
        <div style="display: flex; justify-content: flex-end;">
            <!-- 复制按钮 -->
            <button type="button" class="btn btn-primary" id="copyButton" onclick="copyResult()"
                style="background-color: #5C6D77; border-color: #5C6D77;margin-right:30px ;">复制结果</button>
        </div>



    </div>
    <!-- JavaScript 部分 -->
    <script>
        // 检查用户是否已登录
        function checkLoginStatus() {
            return $.ajax({
                url: '/check_login_status/',
                method: 'GET',
                dataType: 'json'
            });
        }
        // 页面加载完成后检查登录状态
        $(document).ready(function () {
            checkLoginStatus().done(function (data) {
                if (!data.isLoggedIn) {
                    alert("请登录账号！");
                    $('#loginModal').modal('show');
                }
            }).fail(function () {
                console.error("无法检查登录状态。");
            });
        });

        // 提交文案检查的函数
        function submitCheck() {

            // 获取提交按钮元素并禁用它
            const generateButtonEl = document.getElementById("generate");
            generateButtonEl.disabled = true;  // 禁用提交按钮

            const copywritingContent = document.getElementById('copywritingContent').value;
            const referenceContent = document.getElementById('referenceContent').value;

            // 如果没有文案内容，提示用户输入文案
            if (!copywritingContent.trim()) {
                alert("请输入文案内容！");
                $('#loadingIndicator').css("display", "none");
                generateButtonEl.disabled = false;
                return;
            }
            // 检查登录状态
            checkLoginStatus().done(function (data) {
                if (!data.isLoggedIn) {
                    alert("请登录账号！");
                    $('#loginModal').modal('show');
                    $('#loadingIndicator').css("display", "none");
                    generateButtonEl.disabled = false;
                    return;
                }

                // 显示加载指示器
                document.getElementById('loadingIndicator').style.display = 'block';

                // 构造发送的数据
                const inputData = {
                    source: 5,  // 文案检查的请求类型
                    model: "gpt-4o",  // 使用的模型
                    copywritingContent: copywritingContent,
                    referenceContent: referenceContent
                };

                // 发送到 WebSocket 服务器
                if (socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify(inputData));
                } else {
                    // WebSocket 连接未准备好，显示错误信息
                    console.error("连接失败，请刷新页面重试");
                    alert("请登录账号");
                    $('#loginModal').modal('show')
                    $('#loadingIndicator').css("display", "none");
                    generateButtonEl.disabled = false;
                    return;
                }


                messageBuffer = '';
            }).fail(function () {
                console.error("无法检查登录状态。");
                alert("无法确认登录状态，请稍后再试。");
                $('#loadingIndicator').css("display", "none");
                generateButtonEl.disabled = false;
            });
        }

        // WebSocket 接收到消息时
        socket.onmessage = function (event) {
            const data = JSON.parse(event.data);
            // console.log("WebSocket返回的数据：", data);  // 打印返回的数据到控制台

            // 判断目标是否是文案检查的目标（目标ID为5）
            if (data["target"] === 5) {
                const generateButtonEl = document.getElementById("generate");
                const aiResponseEl = document.getElementById('resultContent');

                // 如果返回的数据中有 message 字段
                if ("message" in data) {
                    messageBuffer += data.message;  // 获取文案检查结果

                    // 将检查结果显示到页面
                    if (aiResponseEl) {
                        aiResponseEl.innerHTML = messageBuffer;
                    }
                }

                // 如果返回的数据中有 status 字段，表示请求的状态
                if ("status" in data) {
                    if (data.status === 1) {
                        console.log("文案检查完成");
                    } else if (data.status === 0) {
                        console.log("服务器网络故障");
                        if (aiResponseEl) {
                            aiResponseEl.innerHTML = "服务器网络故障，请稍后再试";
                        }
                    }
                    // 隐藏加载指示器
                    $('#loadingIndicator').css("display", "none");
                    // 启用提交按钮
                    generateButtonEl.disabled = false;
                } else {
                    // 如果没有 status 字段，则默认将加载指示器隐藏并恢复按钮
                    console.log("没有返回状态，加载指示器隐藏");
                    $('#loadingIndicator').css("display", "none");
                    generateButtonEl.disabled = false;
                }
            }
        };



        // 清空文本框内容的函数
        function clearContent(textareaId) {
            // document.getElementById(textareaId).value = '';  // 清空对应文本框的内容
            const textarea = document.getElementById(textareaId);
            textarea.value = '';  // 清空对应文本框的内容
            textarea.style.height = '110px';  // 重置高度为最小高度
        }




        // 复制文案检查结果的函数
        function copyResult() {
            const resultContent = document.getElementById('resultContent').innerText;  // 获取检查结果内容
            if (resultContent) {
                // 使用 Clipboard API 复制内容
                navigator.clipboard.writeText(resultContent).then(function () {
                    alert("文案检查结果已复制到剪贴板！");  // 复制成功后提示
                }).catch(function (error) {
                    console.error("复制失败: ", error);
                    alert("复制失败，请稍后再试");
                });
            } else {
                alert("没有检查结果可以复制");
            }
        }

        // 为文案内容和参考资料的文本框设置动态调整高度
        document.getElementById('copywritingContent').addEventListener('input', function () {
            adjustTextareaHeight(this, 400);
        });

        document.getElementById('referenceContent').addEventListener('input', function () {
            adjustTextareaHeight(this, 450);
        });

        // 动态调整文本框高度的函数
        function adjustTextareaHeight(textarea, maxHeight) {
            textarea.style.height = 'auto';  // 重置高度
            textarea.style.height = Math.min(textarea.scrollHeight, maxHeight) + 'px';  // 设置新高度，最大不超过600px
        }

    </script>
</body>

</html>