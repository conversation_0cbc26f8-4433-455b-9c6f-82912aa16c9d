import os


from dotenv import load_dotenv
from langchain_community.embeddings import DashScopeEmbeddings

load_dotenv()#加载环境变量
tongyi_api_key = os.getenv("DASH_SCOPE_API_KEY")
embeddings_model = DashScopeEmbeddings(
    model="text-embedding-v1", dashscope_api_key=tongyi_api_key
)
#文本嵌入
embeddings =embeddings_model.embed_documents(
[
    "星际穿越: 这是一部探讨字宙奥秘，描述宇航员穿越虫洞寻找人类新家园的科幻电影",
"阿甘正传:这部励志电影描述了一位智力有限但心灵纯净的男子,他意外地参与了多个历史重大事件",
"泰坦尼克号 :讲述了 1912年泰坦尼克号沉船事故中，两位来自不同阶层的年轻人爱情故事的浪漫电影"
]
)


#查询内容嵌入
embedded_query = embeddings_model.embed_query("我想看一部关于字宙探险的电影")
print(embedded_query)
print(len(embeddings),len(embeddings[0]),len(embedded_query))