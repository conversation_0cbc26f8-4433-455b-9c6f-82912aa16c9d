import os
import uuid
from datetime import datetime
from django.shortcuts import render
from django.http import JsonResponse
from filecontrol.models import Fileinfo
from style_content.tools.load_config import Config
from filecontrol.productinfo import Get_text,Get_text_from_pic


config = Config()
file_storage = config.get_config()["file_storage"]
max_file_size = config.get_config()["max_file_size"]
def upload_file(request):
    if request.user.is_authenticated:
        if request.method == 'POST':
            files = request.FILES.getlist('files')
            if len(files) == 0:
                return JsonResponse({'status': -2})
            # 检查文件后缀
            for file in files:
                if not file.name.lower().endswith(('.txt', '.doc', '.pdf')):
                    return JsonResponse({'status': -1})
                elif file.size > max_file_size:
                    return JsonResponse({'status': -3})


            # 保存每个文件
            for file in files:
                loc = file_storage
                # 获取文件扩展名
                _, ext = os.path.splitext(file.name)
                # 在文件名中添加时间戳和随机字符串
                unique_file_name = loc + f'{datetime.now().strftime("%Y%m%d%H%M%S")}_{uuid.uuid4().hex}{ext}'
                print(unique_file_name)
                save_file(file, unique_file_name)
                save_file2sql(file.name, request.user, unique_file_name, file.size)
            return JsonResponse({'status': 1})
        return JsonResponse({'message': 'Only POST method is allowed'}, status=405)
    else:
        return JsonResponse({'status': 0})

def save_file(file,filepath):
    with open(filepath, 'wb+') as destination:
        for chunk in file.chunks():
            destination.write(chunk)

def save_file2sql(filename,user,path,size):
    fileinfo = Fileinfo(document_name=filename,
                    upload_user=user,
                    store_path=path,
                    size=int(size),
    )
    print(fileinfo)
    fileinfo.save()



def filecontrol(request):
    documents = []
    context = {'documents': documents}
    if request.user.is_authenticated:
        fileinfos = Fileinfo.objects.filter(upload_user_id=request.user.id)

        for fileinfo in fileinfos:
            documents.append({"name":fileinfo.document_name,"size":fileinfo.size,"upload_time":fileinfo.create_time,"index_status":0})

    return render(request, "index/filecontrol.html", context)

def get_productinfo(request):
    if request.user.is_authenticated:
        file = request.FILES.get("file")
        select_mode = request.POST.get('select')
        text = ""
        if select_mode == "0":
            text = Get_text(file)
        elif select_mode == "1":
            text = Get_text_from_pic(file)

        # stream = io.BytesIO(file.read())  # 创建一个二进制流
        # doc = fitz.open("pdf", stream)
        # text = ""
        # for page in doc:
        #     text += page.get_text()
        # doc.close()


        from openai import OpenAI

        client = OpenAI(api_key="sk-432a28db77fe43d288368f3525db2742", base_url="https://api.deepseek.com/")
        template = f"""以下是一个产品介绍手册中的内容，你需要将产品的主要信息整理出来以便后续产品宣传文案的撰写。你的回答中只需要包含产品相关信息，不需要开头语和结尾语。<产品手册>{text}</产品手册>"""
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": template},
            ],
        )
        # print(response.choices[0].message.content)
        return JsonResponse({'status': 1, "response": response.choices[0].message.content})
    else:
        return JsonResponse({'status': 0})



