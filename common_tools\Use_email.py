import smtplib
from email.mime.text import MIMEText
from email.utils import formataddr
from typing import List, Union


class EmailSender:
    """
    一个用于发送电子邮件的类，支持QQ邮箱和其他SMTP服务。
    完美解决QQ邮箱假错误问题，并提供灵活的配置。
    """

    def __init__(self,
                 sender_email: str,
                 sender_password: str,  # 必须是授权码，不是邮箱密码
                 smtp_server: str,
                 smtp_port: int = 587):
        """
        初始化 EmailSender 实例。

        :param sender_email: 发件人邮箱地址。
        :param sender_password: 发件人邮箱的授权码（或密码，取决于服务提供商）。
        :param smtp_server: SMTP 服务器地址。
        :param smtp_port: SMTP 服务器端口，默认为 587。
        """
        self.sender_email = sender_email
        self.sender_password = sender_password
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port

    def send_email(
            self,
            recipient: Union[str, List[str]],
            subject: str,
            content: str,
            sender_name: str = "Python发件人",
            recipient_name: str = "收件人"  # 这个参数目前在代码中未使用，但保留以防未来扩展
    ) -> dict:
        """
        发送邮件到指定邮箱。

        :param recipient: 收件人邮箱地址（字符串或列表）。
        :param subject: 邮件主题。
        :param content: 邮件正文。
        :param sender_name: 发件人在邮件中显示的名称，默认为 "Python发件人"。
        :param recipient_name: 收件人在邮件中显示的名称，默认为 "收件人"（目前未使用）。
        :return: 包含发送状态的字典。
        """
        # 标准化收件人格式
        if isinstance(recipient, str):
            recipient_list = [recipient]
        else:
            recipient_list = recipient

        recipient_list = [r.strip() for r in recipient_list if r.strip()]

        if not recipient_list:
            return {"status": False, "message": "收件人地址不能为空", "recipient": []}

        try:
            # 创建邮件
            message = MIMEText(content, "plain", "utf-8")
            message["From"] = formataddr((sender_name, self.sender_email))
            message["To"] = ", ".join(recipient_list)  # 多个收件人用逗号分隔
            message["Subject"] = subject

            # 发送流程
            # 对于QQ邮箱，通常推荐使用 SSL 连接，端口 465 或 587（StartTLS）。
            # 这里的实现兼容了 StartTLS (587)，如果服务明确要求 SSL (465)，需要调整。
            if self.smtp_port == 465:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()  # 启用 TLS 加密

            server.login(self.sender_email, self.sender_password)

            # 发送邮件
            server.sendmail(self.sender_email, recipient_list, message.as_string())

            # 手动处理连接关闭（避免假错误影响结果判断）
            try:
                server.quit()
            except Exception as e:
                # 尝试关闭失败，但邮件已发送，可以记录日志而非抛出错误
                print(f"Warning: Failed to gracefully quit SMTP server, but email likely sent. Error: {e}")
                server.close()  # 确保关闭连接

            # 只要sendmail没报错就视为成功
            return {"status": True, "message": "邮件发送成功", "recipient": recipient_list}

        except smtplib.SMTPResponseException as e:
            return {"status": False, "message": f"SMTP错误({e.smtp_code}): {e.smtp_error.decode('utf-8', 'ignore')}",
                    "recipient": recipient_list}
        except Exception as e:
            return {"status": False, "message": f"发送失败: {str(e)}", "recipient": recipient_list}


if __name__ == "__main__":
    # --- QQ 邮箱发送示例 ---
    print("--- QQ 邮箱发送示例 ---")
    qq_sender = EmailSender(
        sender_email="<EMAIL>",
        sender_password="iknyosisazkhdjdh",  # 替换为你的QQ邮箱授权码
        smtp_server="smtp.qq.com",
        smtp_port=587
    )

    # 单个收件人
    result_qq_single = qq_sender.send_email(
        recipient=["<EMAIL>"],
        subject="来自 Python 类的问候",
        content="你好，这是一封通过 EmailSender 类发送的测试邮件！"
    )
    print(f"QQ 单个收件人发送结果: {result_qq_single}")
    print("-" * 30)

    # 多个收件人
    result_qq_multiple = qq_sender.send_email(
        recipient=["<EMAIL>", "<EMAIL>"],  # 替换为有效邮箱
        subject="Python 类群发测试",
        content="这是一封使用 EmailSender 类群发的邮件。",
        sender_name="我的自动化系统"
    )
    print(f"QQ 多个收件人发送结果: {result_qq_multiple}")
    print("-" * 30)

    # --- 假设的企业邮箱发送示例 (以 Office365 为例) ---
    # 请注意：你需要替换为你的实际企业邮箱配置和授权码/密码
    print("\n--- 企业邮箱发送示例 (以 Office365 为例) ---")
    try:
        enterprise_sender = EmailSender(
            sender_email="<EMAIL>",  # 替换为你的企业邮箱
            sender_password="your_enterprise_app_password",  # 替换为你的企业邮箱授权码或密码
            smtp_server="smtp.office365.com",
            smtp_port=587
        )

        result_enterprise = enterprise_sender.send_email(
            recipient="<EMAIL>",  # 替换为接收邮件的地址
            subject="企业邮件报告",
            content="这是通过企业邮箱发送的报告内容。",
            sender_name="企业通知中心"
        )
        print(f"企业邮箱发送结果: {result_enterprise}")
    except Exception as e:
        print(f"企业邮箱示例配置错误或发送失败：{e}")
        print(
            "请替换 '<EMAIL>', 'your_enterprise_app_password', '<EMAIL>' 为你的实际信息。")
    print("-" * 30)