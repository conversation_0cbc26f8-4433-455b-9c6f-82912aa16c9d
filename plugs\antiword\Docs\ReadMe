                    ___        _   _                       _
                   / _ \      | | (_)                     | |
                  | |_| |_ __ | |_ ___      _____  _ __ __| |
                  |  _  | '_ \| __| \ \ /\ / / _ \| '__/ _` |
                  | | | | | | | |_| |\ V  V / (_) | | | (_| |
                  |_| |_|_| |_|\__|_| \_/\_/ \___/|_|  \__,_|

Antiword
========

Version 0.37 (21 Oct 2005)
--------------------------     

Introduction
------------

Antiword is an application for displaying Microsoft(R) Word documents.


License
-------

This program is distributed under the GNU General Public License - see the
accompanying COPYING file for more details.


Problems
--------

Any bugs found should be reported to the author with full details of how to
get the problem to occur, but don't *expect* support for a product that you
have not paid for!

Please include Antiword's version number and version date, otherwise you
make it impossible for the author to help.


Thanks To
---------

<PERSON> <<EMAIL>> creator of "catdoc"
<PERSON> <<EMAIL>> creator of "word2x"
<PERSON> <<EMAIL>> creator of "laola" and "elser"
Caolan McNamara <<EMAIL>> creator of "mswordview"
Andrew Scriven <<EMAIL>> creator of "OLEdecode"
Craig Southeren <<EMAIL>> creator of "nenscript"
Thomas Merz <<EMAIL>> creator of "jpeg2ps"
Ulrich von Zadow <<EMAIL>> creator of "paintlib"


Contributors
------------

ISO-8859-2 support by: Pawel Turnau <<EMAIL>>
Character set mapping by: Dmitry Chernyak
                          <<EMAIL>>
UTF-8 support by: Karl Koehler <<EMAIL>> and
                  Markus Kuhn <<EMAIL>>
PostScript Cyrillic by: Alexander Belyaev <<EMAIL>>


Ports
-----

Antiword was ported to BeOS by Pete Goodeve <<EMAIL>>
Antiword was ported to OS/2 by Dave Yeo <<EMAIL>>
Antiword was ported to Mac OS X by Ronaldo Nascimento <<EMAIL>>
Antiword was ported to Amiga by Raffaele Pisapia <<EMAIL>>
Antiword was ported to VMS by Joseph Huber <<EMAIL>>
Antiword was ported to NetWare by Guenter Knauf <<EMAIL>>
Antiword was ported to EPOC by Max Tomin <<EMAIL>>
Antiword was ported to Zaurus PDA by Piotr Jachimczyk
                                     <<EMAIL>>
Antiword was ported to DOS by myself ;-)
Yen-Ming Lee <<EMAIL>> is the maintainer of the FreeBSD version of
Antiword.


Acknowledgements
----------------

Microsoft is a registered trademark and Windows is a trademark of Microsoft
Corporation.
UNIX is a registered trademark of the X/Open Company, Ltd.
Linux is a registered trademark of Linus Torvalds.
Postscript is a trademark of Adobe Systems Incorporated.
All other trademarks are acknowledged.


Future Versions
---------------

If you have any comments, bug reports or suggestions for future versions
don't hesitate to write to me.
New versions of the program will only be available if sufficient people
are using this program. So let me know!


Most recent version
-------------------

Most recent version of Antiword can be found on the author's website:
==>>  http://www.winfield.demon.nl/index.html  <<==
==>>  http://antiword.cjb.net/  <<==


Author
------

The author can be reached by e-mail:
<EMAIL>
<EMAIL>

But PLEASE read the FAQ before you write!!
