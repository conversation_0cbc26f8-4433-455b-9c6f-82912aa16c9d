import sys
import fitz  # PyMuPDF
from docx import Document
from openai import OpenAI
def extract_text_from_pdf(pdf_path):
    """Extract text from a PDF file."""
    doc = fitz.open(pdf_path)
    text = ""
    for page in doc:
        text += page.get_text()
    doc.close()
    return text

def extract_text_from_docx(docx_path):
    """Extract text from a Word document."""
    doc = Document(docx_path)
    text = "\n".join([paragraph.text for paragraph in doc.paragraphs])
    return text

def main():
    if len(sys.argv) != 3:
        print("Usage: python script.py <file_type> <file_path>")
        sys.exit(1)

    file_type = sys.argv[1].lower()
    file_path = sys.argv[2]

    if file_type == 'pdf':
        text = extract_text_from_pdf(file_path)
    elif file_type == 'docx':
        text = extract_text_from_docx(file_path)
    else:
        print("Unsupported file type. Use 'pdf' or 'docx'.")
        sys.exit(1)

    print(text)

def get_productinfo():

    client = OpenAI(api_key="sk-432a28db77fe43d288368f3525db2742", base_url="https://api.deepseek.com/")
    content = extract_text_from_pdf("product_inform.pdf")
    template = f"""以下是一个产品介绍手册中的内容，你需要对其进行整理归纳，将对产品宣传文案撰写有帮助的信息点整理出来。你的回答中只需要包含产品相关信息，不需要开头语和结尾语。若手册中不包含任何产品信息，请回复“文件中未找到相关产品信息”。<产品手册>{content}</产品手册>"""

    response = client.chat.completions.create(
        model="deepseek-chat",
        messages=[
            {"role": "system", "content": "You are a helpful assistant"},
            {"role": "user", "content": template},
        ],
    )

    return response.choices[0].message.content

def writting_route(product_info):
    client = OpenAI(api_key="sk-432a28db77fe43d288368f3525db2742", base_url="https://api.deepseek.com/")
    template = f"""你是一位文案撰写专家，现在你需要为产品文案的撰写提供写作思路。写作思路需要包含标题、摘要、和文案切入点。**标题是整个文案的题目。**摘要是对整个文案简短的总结。**正文内容需要根据切入点分为几个小段，你需要提供每个小段的切入点思路。<产品信息>{product_info}</产品信息>"""

    response = client.chat.completions.create(
        model="deepseek-chat",
        messages=[
            {"role": "system", "content": "You are a helpful assistant"},
            {"role": "user", "content": template},
        ],
    )
    return response.choices[0].message.content


if __name__ == "__main__":
    pro = get_productinfo()
    print(pro)
    print(writting_route(pro))