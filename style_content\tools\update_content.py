import datetime
import time
from datetime import date

from style_content.models import CreatedContent


async def aupdate_content(user, product_information, Outline, Final_output):
    temp = CreatedContent(userid=user,product_information=product_information,
                          Outline=Outline, Final_output=Final_output, create_time=datetime.datetime.now())
    num_record = await CreatedContent.objects.filter(userid=user).acount()
    if num_record >= 10:
        # 删除最早的一条记录
        oldest_content = await CreatedContent.objects.filter(userid=user).order_by('create_time').afirst()
        if oldest_content:
            await oldest_content.adelete()
            await temp.asave()
    else:
        await temp.asave()
