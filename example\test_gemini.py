import requests

url = "http://192.168.31.94:1234/v1/chat/completions"
headers = {
    "Content-Type": "application/json"
}
data = {
    "model": "gemma-3-1b-it",
    "messages": [
        {
            "role": "system",
            "content": "Always answer in rhymes. Today is Thursday"
        },
        {
            "role": "user",
            "content": "What day is it today?"
        }
    ],
    "temperature": 0.7,
    "max_tokens": -1,
    "stream": False
}

response = requests.post(url, headers=headers, json=data)

# 打印响应
print(response.json())