def extract_follow_ups_and_intermediate_answers(text):
    # 提取子问题和答案到两个列表中
    lines = text.strip().split('\n')

    # 存储提取的 "Follow up:" 内容
    follow_ups = []
    Intermediate_answer = []
    final_answer = ""

    # 遍历每一行
    for line in lines:
        # 检查当前行是否以 "Follow up:" 开头
        if line.startswith("Follow up:"):
            # 提取 "Follow up:" 后的内容，并去掉前后的空格
            follow_up_content = line[len("Follow up:"):].strip()
            follow_ups.append(follow_up_content)  # 添加到列表中
        elif line.startswith("Intermediate answer:"):
            Intermediate_answer.append(line[len("Intermediate answer:"):].strip())
        elif line.startswith("So the final answer is:"):
            final_answer = line[len("So the final answer is:"):].strip()


    # 返回提取的 "Follow up:" 内容


    return follow_ups, Intermediate_answer,final_answer


# 示例字符串
ret_text = """Yes.
Follow up: Who is the reigning men's U.S. Open champion?
Intermediate answer: As of October 2021, the reigning men's U.S. Open champion is <PERSON><PERSON>.
Follow up: Where is <PERSON><PERSON> from?
Intermediate answer: <PERSON><PERSON> is from Moscow, Russia.
So the final answer is: Moscow, Russia"""

# 调用函数并打印结果

from serpapi import GoogleSearch
from IPython.utils import io
cur_prompt = """ """


intermediate="\nIntermediate answer:"
followup="Follow up:"


def google(question):
    print("google_search")
    """
    使用SerpApi搜索问题并提取答案。

    参数:
    question (str): 需要查询的问题字符串，例如："What is the boiling point of water?"。

    返回:
    str or None: 如果找到答案则返回字符串（如"100°C"），否则返回None。

    注意事项:
    - 需提前安装SerpApi库：`pip install google-search-results`
    - 需配置有效的SerpApi API密钥（serpapi_key）
    - 依赖SerpApi的响应结构，若API返回格式变更可能需要调整代码
    - 优先提取答案框内容，其次为高亮片段或首个自然搜索结果摘要
    """

    # 配置SerpApi请求参数
    params = {
        "api_key": serpapi_key,  # 替换为你的SerpApi API密钥
        "engine": "google",  # 使用Google搜索引擎
        "q": question,  # 查询的问题
        "google_domain": "google.com",
        "gl": "us",  # 国家代码（美国）
        "hl": "en"  # 语言设置（英语）
    }

    # 抑制GoogleSearch的输出（如进度条等）
    with io.capture_output() as captured:
        search = GoogleSearch(params)  # 初始化搜索对象
        res = search.get_dict()  # 获取搜索结果的字典格式响应
        print(res)

    # 从响应中提取答案的优先级逻辑：
    # 1. 优先检查答案框中的直接答案（如计算、定义等）
    print(res)
    print(res.keys())
    if 'answer_box' in res.keys() and 'answer' in res['answer_box'].keys():
        toret = res['answer_box']['answer']
    # 2. 次选答案框中的摘要片段（可能包含关键信息）
    elif 'answer_box' in res.keys() and 'snippet' in res['answer_box'].keys():
        toret = res['answer_box']['snippet']
    # 3. 若存在高亮关键词片段，提取第一个
    elif 'answer_box' in res.keys() and 'snippet_highlighted_words' in res['answer_box'].keys():
        toret = res['answer_box']["snippet_highlighted_words"][0]
    # 4. 若以上均无，则取首个自然搜索结果的摘要
    elif 'organic_results' in res and len(res['organic_results']) > 0 and 'snippet' in res["organic_results"][0].keys():
        toret = res["organic_results"][0]['snippet']
    else:
        toret = None  # 未找到答案时返回None

    return toret

def get_answer(question):
    """
    使用外部API（如搜索引擎）获取问题的答案。

    参数:
    question (str): 需要查询的问题字符串，例如："What is the boiling point of water?"。

    返回:
    str or None: 如果找到答案则返回字符串（如"100°C"），否则返回None。
    """


    return google(question)


serpapi_key = '61da5eaf67a09ac0ed1397dee8debd0f7749e6551c545be8bcedc572d6c3f35b'
res = get_answer('水泥地耐磨')
print(res)

# follow_up_questions,Intermediate_answers = extract_follow_ups_and_intermediate_answers(ret_text)
# print(follow_up_questions)
# print(Intermediate_answers)
# for i in range(len(follow_up_questions)):
#         # 通过外部API（如搜索引擎）获取问题的答案
#     external_answer = get_answer(follow_up_questions[i])
#     if external_answer is not None:
#             # 将外部答案作为中间答案追加到当前prompt中
#         cur_prompt += '\n' +followup+' '+follow_up_questions[i] + intermediate + ' ' + external_answer + '.'
#     else:
#             # 极少数情况下，外部API无答案，直接让GPT生成后续内容
#         cur_prompt += '\n' +followup+' '+follow_up_questions[i]  + intermediate + ' ' + Intermediate_answers[i] + '.' # 追加中间答案的前缀
# print(cur_prompt)


