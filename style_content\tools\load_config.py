from django.conf import settings

class Config:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(Config, cls).__new__(cls, *args, **kwargs)
            cls._instance.load_config()
        return cls._instance

    def load_config(self):
        import yaml
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        config_path = os.path.join(current_dir, '../../config.yaml')
        with open(config_path, 'r', encoding="utf-8") as file:
            self.config = yaml.safe_load(file)

    def get_config(self):
        return self.config