# Generated by Django 5.0.1 on 2024-04-02 18:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0002_delete_userinfo'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Usage',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('use_time', models.DateField()),
                ('token_num', models.IntegerField()),
                ('use_model', models.Char<PERSON>ield(max_length=20)),
                ('userid', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddConstraint(
            model_name='usage',
            constraint=models.UniqueConstraint(fields=('userid', 'use_time', 'use_model'), name='unique_userid_model_time'),
        ),
    ]
