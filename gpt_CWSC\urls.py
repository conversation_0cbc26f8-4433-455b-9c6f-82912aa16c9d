"""gpt_CWSC URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path

import accounts.views
import base.views
import filecontrol.views
import style_content.views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('changePlatform/', style_content.views.change_platform),
    path('', base.views.index),
    path('copywritting/', style_content.views.copywritting),
    path("checkCopywriting/",style_content.views.checkCopywriting),
    path('filecontrol/', filecontrol.views.filecontrol),
    path('register/', accounts.views.register),
    path('login/', accounts.views.login_user),
    path('logout/', accounts.views.log_out),
    path('upload/', filecontrol.views.upload_file),
    path('get_usage/',accounts.views.get_usage),
    path("productinfo/",filecontrol.views.get_productinfo),
    path("optimize/",style_content.views.optimize),
    path("chat/",style_content.views.chat),
    path("wordtoimages_quary/",style_content.views.wordtoimages_quary),
    path("demands_for_stars_birthday/",style_content.views.demands_for_stars_birthday),
    path("find_usage/",accounts.views.find_usage),
    path('check_login_status/',accounts.views.check_login_status),
]