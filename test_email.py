import smtplib
from email.mime.text import MIMEText
from email.utils import formataddr
from typing import List, Union


def send_email(
        recipient: Union[str, List[str]],
        subject: str,
        content: str,
        sender_name: str = "Python发件人",
        recipient_name: str = "收件人"
) -> dict:
    """
    发送邮件到指定邮箱（完美解决QQ邮箱假错误问题）

    :param recipient: 收件人邮箱地址（字符串或列表）
    :param subject: 邮件主题
    :param content: 邮件正文
    :return: 包含发送状态的字典
    """
    config = {
        "SENDER_EMAIL": "<EMAIL>",
        "SENDER_PASSWORD": "iknyosisazkhdjdh",  # 必须使用授权码，不是QQ密码
        "SMTP_SERVER": "smtp.qq.com",
        "SMTP_PORT": 587
    }

    # 标准化收件人格式
    if isinstance(recipient, str):
        recipient = [recipient]
    recipient = [r.strip() for r in recipient if r.strip()]

    if not recipient:
        return {"status": False, "message": "收件人地址不能为空", "recipient": []}

    try:
        # 创建邮件
        message = MIMEText(content, "plain", "utf-8")
        message["From"] = formataddr((sender_name, config["SENDER_EMAIL"]))
        message["To"] = ", ".join(recipient)  # 多个收件人用逗号分隔
        message["Subject"] = subject

        # 发送流程（关键修改点）
        server = smtplib.SMTP_SSL(config["SMTP_SERVER"], config["SMTP_PORT"])
        server.login(config["SENDER_EMAIL"], config["SENDER_PASSWORD"])

        # 重点：分离发送和关闭操作
        server.sendmail(config["SENDER_EMAIL"], recipient, message.as_string())

        # 手动处理连接关闭（避免假错误影响结果判断）
        try:
            server.quit()
        except:
            server.close()

        # 只要sendmail没报错就视为成功
        return {"status": True, "message": "邮件发送成功", "recipient": recipient}

    except smtplib.SMTPResponseException as e:
        return {"status": False, "message": f"SMTP错误({e.smtp_code}): {e.smtp_error.decode('utf-8', 'ignore')}",
                "recipient": recipient}
    except Exception as e:
        return {"status": False, "message": f"发送失败: {str(e)}", "recipient": recipient}


# 使用示例
if __name__ == "__main__":
    result = send_email(
        recipient="<EMAIL>",
        subject="明星生日",
        content="这是一封测试邮件内容"
    )
    print(result)


# 多收件人示例
    result = send_email(
        recipient=["<EMAIL>", "<EMAIL>"],
        subject="群发测试",
        content="这是群发邮件内容",
        sender_name="系统通知"
    )
    print(result)



# # 企业邮箱发送示例
# report_result = send_sales_report(
#     recipient="<EMAIL>",
#     sales_data_path="sales_data.csv",
#     sender_email="<EMAIL>",
#     sender_password="your_secure_password",
#     sender_name="销售数据分析系统",
#     smtp_server="smtp.office365.com",  # 以Office365为例
#     smtp_port=587
# )
#
# print(report_result)