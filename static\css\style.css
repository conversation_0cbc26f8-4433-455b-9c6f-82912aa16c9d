/* style.css */

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
}

.editor-container {

    width: 80%;
    margin: 0 auto;
    height: 90%;
    position: relative;
    /* 确保可以作为子元素绝对定位的参照 */
}

#editor {
    position: relative;
    /* 允许内部绝对定位 */
    min-height: 200px;
    padding: 10px;
    border: 1px solid #ddd;
    margin-bottom: 10px;
    margin-top: 100px;
    /* 添加这行，使轮廓变大 */
    max-height: 480px;
    max-width: 1200px;
    /* 设置最大高度 */
    overflow-y: auto;
    /* 添加垂直滚动条 */
    border-radius: 5px;
    box-shadow: 0 5px 10px rgba(0,0,0,0.1);
}

#editIcon {
    position: relative;
    cursor: pointer;
}

.edit-button {
    background: url("/static/pic/R.png") no-repeat center center;
    background-size: contain;
    /* 确保图标适应按钮大小而不失真 */
    width: 30px;
    height: 30px;
    border: none;
    cursor: pointer;
    outline: none;
}

.copy-button {
    position: absolute;
    right: 5px;
    font-size: 14px;
    padding: 5px 10px;
    border: none;
    background-color: transparent;
    color: #060000;
    border-radius: 5px;
    cursor: pointer;
}

.submit-button {
    background-color: hsl(196, 77%, 42%);
    /* 红色背景 */
    color: white;
    /* 白色文字 */
    border: none;
    /* 无边框 */
    padding: 10px 20px;
    /* 内边距 */
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 2px 2px;
    cursor: pointer;
    border-radius: 8px;
    /* 圆角边框 */
}

.cleanIN-button {
    background-color: transparent;
    /* 无背景色 */
    color: black;
    /* 黑色文字 */
    padding: 10px 20px;
    /* 内边距 */
    border: none;
    /* 无边框 */
    cursor: pointer;
    /* 鼠标悬停时的光标样式 */
    font-size: 16px;
    /* 字体大小 */
    transition: background-color 0.3s, border 0.3s;
    /* 平滑过渡效果 */
}

/* 鼠标悬停样式 */
.cleanIN-button:hover {
    background-color: #e0e0e0;
    /* 灰色背景 */
    border: 2px solid #bdbdbd;
    /* 灰色边框 */
    color: black;
    /* 文字颜色变为黑色 */
}

.insert-button {
    background-color: hsl(196, 77%, 42%);
    /* 红色背景 */
    color: white;
    /* 白色文字 */
    border: none;
    /* 无边框 */
    padding: 10px 20px;
    /* 内边距 */
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 2px 2px;
    cursor: pointer;
    border-radius: 8px;
    /* 圆角边框 */
}

/* 基本按钮样式 */
.retry-button {
    background-color: transparent;
    /* 无背景色 */
    color: black;
    /* 黑色文字 */
    padding: 10px 20px;
    /* 内边距 */
    border: none;
    /* 无边框 */
    cursor: pointer;
    /* 鼠标悬停时的光标样式 */
    font-size: 16px;
    /* 字体大小 */
    transition: background-color 0.3s, border 0.3s;
    /* 平滑过渡效果 */
}

/* 鼠标悬停样式 */
.retry-button:hover {
    background-color: #e0e0e0;
    /* 灰色背景 */
    border: 2px solid #bdbdbd;
    /* 灰色边框 */
    color: black;
    /* 文字颜色变为黑色 */
}

.popup {
    position: fixed;
    top: 40%;
    left: 60%;
    transform: translate(-50%, -50%);
    /* 将弹窗居中显示 */
    width: 50%;
    max-width: 500px;
    /* 设置最大宽度 */
    background-color: #fff;
    border: 1px solid #ccc;
    z-index: 1000;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    /* 更柔和的阴影 */
    border-radius: 8px;
    /* 圆角边框 */
    animation: fadeIn 0.3s;
    /* 淡入效果 */
}

.popup-header {
    padding: 10px;
    background: #f2f2f2;
    color: #333;
    cursor: move;
    /* 显示可移动的鼠标光标 */
}

.popup-header h2 {
    display: flex;
    align-items: center;
    justify-content: center;
    /* 将内容水平居中 */
}

.popup-body {
    padding: 10px;
}

.close {
    float: right;
    cursor: pointer;
    cursor: pointer;
    font-size: 24px;
    /* 增大字体大小 */
    padding: 10px;
    /* 增大内边距 */
    margin: -10px -10px 0 0;
    /* 控制按钮与标题栏边缘的距离 */
}

textarea {
    width: 100%;
    background: #fff;
    padding: 5px;
    border-radius: 5px;
}


#inputText {
    width: 100%;
    min-height: 50px;
    /* 设置最小高度 */
    max-height: 200px;
    /* 设置最大高度 */
    overflow-y: auto;
    /* 添加垂直滚动条 */
    padding: 5px;
    /* 添加内边距 */
    box-sizing: border-box;
    /* 包括内边距在宽度和高度计算中 */
    resize: vertical;
    /* 允许用户仅垂直调整文本区域的大小 */
}
    #loading-spinner3 {
    position: absolute;
    top: calc((100% - 20px) / 2);
    left: calc((100% - 20px) / 2);
    transform: translate(-50%, -50%);
    animation: spin 1s linear infinite;
}

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .loadingIndicator3 {
    border: 4px solid #f3f3f3; /* Light grey */
    border-top: 4px solid #3498db; /* Blue */
    border-radius: 50%;
    width: 15px;
    height: 15px;
    animation: spin 1s linear infinite;
    margin-left: 20px;
    margin-bottom: 10px;
    }