{"cells": [{"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true}, "outputs": [], "source": ["from datasets import Dataset\n", "from llama_index.core import PromptTemplate\n", "from llama_index.llms.openai import OpenAI\n", "import os\n", "import openai\n", "os.environ['OPENAI_API_KEY'] = \"\"\n", "openai.api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "product1 = \"\"\"\n", "标题：\n", "新品上市 | 出发向户外，徒步有新奇\n", "摘要：\n", "SALMON全新360系列\n", "连结自然都市，打造山系松弛\n", "X ULTRA 360 EDGE MID GTX (高 帮款)\n", "X ULTRA 360 EDGE GTX (低帮款)\n", "承袭专业性能，助力每一位户外人\n", "内容：\n", "沉浸洒脱 美学出新\n", "汲取山野灵感，上演探索意趣\n", "四色还原自然缤纷\n", "诠释户外徒步穿搭“新美学\n", "网状鞋面交织TPU亮面鞋头\n", "实现功能性与设计感的巧妙平衡\n", "自在漫游蜿蜒山路\n", "造型百变，一双以应万变\n", "\n", "保驾护航 专业出新\n", "延续X ULTRA系列产品硬核科技\n", "以专业实力\n", "打造全新徒步装备\n", "Advanced Chassis底盘科技\n", "确保徒步过程中的稳定支撑及防扭转\n", "包裹足跟的同时\n", "保持前脚掌灵活度\n", "有效提升运动流畅度\n", "通过GORE-TEX技术\n", "有效阻挡水分、风沙等外部侵害\n", "同时保持鞋身内部空气流通\n", "环境潮湿恶劣\n", "脚步干爽依旧\n", "\n", "缤纷万象 体验出新\n", "出走自然天地，找寻新奇意趣\n", "X ULTRA 360 EDGE延续SAL OMON户外基因\n", "强大产品功能，适配多重场景\n", "崎岖山路与都市街道无缝切换\n", "\n", "走出一路新奇\n", "深入林间山路，\n", "亦或游历街巷\n", "新的一年，你又准备走去哪里?\n", "徒步所至，新奇无穷\n", "自在行走，探寻户外全新可能\"\"\"\n", "product2 = \"\"\"\n", "标题：\n", "唤山者系列 | 登顶四姑娘山巅，重现跑者热力\n", "段落1：\n", "山就在那里\n", "从古至今，山的力量，山的精神\n", "早已存在我们每个人的内心深处\n", "即使山离你再远\n", "现在，\n", "山的精神\n", "也将被再度唤醒，点燃，和重新演绎\n", "我们以中国三座名山为灵感\n", "推出SALOMON r唤山者」系列\n", "将山的精神，与内心重新连接\n", "段落2：\n", "「唤山者」系列首发鞋款XT-6\n", "四姑娘山\n", "我们捕捉攀登四姑娘山的热情\n", "化为热力图，点燃你探索不息的向往\n", "段落3：\n", "从高山热力图汲取设计灵感\n", "点燃探索不息的向往\n", "捕捉跑者攀登热情，将山与人合二为一\n", "段落4：\n", "高饱和色系，幻化纷繁色彩\n", "以独特视角，演绎四姑娘山海拔高度\n", "用绚烂色彩，展露对山野精神的无限向往\n", "段落5：\n", "以高山热力图谱为鞋面着以斑斓之色\n", "与绿、紫、红三色鞋底和谐相应\n", "鞋盒同款印花，捕捉越野跑者炽热之心\n", "独立编号山魂卡，标志无可替代的山地灵魂\n", "段落6：\n", "越野\n", "踏山而行\n", "为恶劣环境下的长距离比赛而生的XT-6\n", "具备优越的越野性能\n", "段落7：\n", "舒适缓震ACS中底科技\n", "(AGILE CHASSIS SYSTEM)\n", "确保攀登之时，脚感舒适稳定\n", "段落8：\n", "搭载Contagrip@高抓地力外底\n", "与TPU薄膜相互配合\n", "耐磨同时保证优越的抓地性能\n", "翻越山川，无惧地形\n", "段落9：\n", "唤醒山的灵魂，点燃山的活力\n", "汲取三大名山之精神，缔造三双经典跑鞋\n", "SALOMON唤山者 」系列未完待续\n", "首发鞋款XT- 6四姑娘山\n", "8月28日即将正式发售\"\"\"\n", "prompt = PromptTemplate(\"\"\"\n", "现在你是一名文案撰写专家，首先你需要分析示例文案的风格，然后通过给定的文案信息对其进行仿写。\n", "<文案要求>\n", "1.文案每句话都需要占用单独一行。\n", "2.风格与示例文案保持一致，发挥创造力。\n", "3.使用用户给定的文案信息。\n", "4.语言保持生动优美，运用情感渲染、故事叙述、形象描绘、色彩表达、情感连接等方式介绍产品。\n", "</文案要求>\n", "<示例文案>\n", "{product}\n", "</示例文案>\n", "<文案信息>\n", "{input_data}\n", "</文案信息>\"\"\")\n", "\n", "input_data = \"\"\"1. <PERSON><PERSON> Jah  是Salomon 在巴黎的长期深入合作的青年文化社群，以非洲素食餐厅为根据地，分享美食，艺术和时尚，音乐和户外热情。\n", "2. 这次联名款的创意：西非神话故事 Kankourang。\n", "3. 选用的鞋款:ACS PRO\n", "法国先锋户外品牌 Salomon与不同领域的创意社区接触，与巴黎青年社区 Jah Jah 合作推出ACS PRO FOR JAH JAH，展现巴黎青年自由乐观的生活态度，向年轻人和少数族裔介绍新的户外运动实践。Salomon Acs Pro For Jah Jah合作款将于10月14日正式发售。\n", "\n", "Salomon X Jah Jah的合作探索西非神话与传统，重温西非文化故事<kankourang> ，讲述一个孩子踏上内心旅程的故事。作为祖先习俗和传统的守护者，Kankourang的存在旨在为信仰者们维护社会秩序和社区团结。展现了西非文化遗产在当今的艺术和社区中引人入胜的力量。\n", "\n", "此番合作款反映了 JAH JAH 宇宙的价值观，重构艺术创作与功能性的结合，低调的鞋面注入了JAH JAH标志性的红、黄、绿三色，为平凡日常生活的带来色彩振动。\n", "\n", "鞋面采用坚固的 Kurim 结构，提供稳定、坚固的包覆效果。精心设计的内外侧开口提供流畅的透气性，搭配不断升级的Agile Chassis System缓震系统，以经典的轮廓为基础，打造充满年轻活力和冒险精神的鞋款。\"\"\"\n"]}, {"cell_type": "code", "execution_count": 28, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["标题：\n", "ACS PRO FOR JAH JAH | 融合西非神话，探索户外新境界\n", "\n", "段落1：\n", "<PERSON>ah Jah，那个充满活力的巴黎青年文化社群\n", "在非洲素食餐厅里分享美食、艺术、时尚、音乐和户外热情\n", "他们与Salomon的深入合作，孕育出ACS PRO FOR JAH JAH\n", "展现自由乐观的生活态度，引领新的户外运动实践\n", "\n", "段落2：\n", "这次联名款的创意源自西非神话故事Kankourang\n", "一个孩子踏上内心旅程的故事\n", "Kankourang作为祖先习俗和传统的守护者\n", "维护社会秩序和社区团结，展现西非文化的力量\n", "\n", "段落3：\n", "Salomon X Jah Jah的合作，重温西非文化故事\n", "将Kankourang的传统融入鞋款ACS PRO\n", "鞋面低调注入JAH JAH标志性的红、黄、绿三色\n", "为日常生活带来色彩振动，展现艺术与功能的完美结合\n", "\n", "段落4：\n", "鞋面采用坚固的Kurim结构，稳定包覆效果出色\n", "内外侧开口设计提供流畅透气性\n", "搭配Agile Chassis System缓震系统，打造充满冒险精神的鞋款\n", "经典轮廓下，隐藏着年轻活力的力量\n", "\n", "段落5：\n", "Salomon Acs Pro For Jah Jah合作款即将于10月14日正式发售\n", "融合西非神话，探索户外新境界\n", "让我们一同感受Jah Jah的热情，重构艺术与功能的奇妙融合\n", "迎接新的户外运动实践，展现自由乐观的生活态度。\n"]}], "source": ["llm = OpenAI(model=\"gpt-3.5-turbo-0125\", temperature=0.1)\n", "\n", "stream = await llm.apredict(prompt,input_data=input_data,product=product2)\n", "print(stream)"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 26, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["结构分析：\n", "该文案分为标题、摘要和内容三部分，标题直接点明了新品上市的主题，摘要简洁明了地介绍了新品系列和其特点，内容部分分为三个小标题，分别从美学、专业性能和体验三个方面展示了新品的特点。\n", "\n", "修辞手法分析：\n", "1. 比喻：将徒步穿搭比作“新美学”，将X ULTRA 360 EDGE系列产品比作“延续SALOMON户外基因”。\n", "2. 排比：连续使用了“沉浸洒脱”、“美学出新”、“四色还原”等词语，增加了文案的节奏感和吸引力。\n", "3. 拟人：将鞋子描述为“自在漫游蜿蜒山路”，增加了产品的亲和力和活力感。\n", "\n", "语言风格分析：\n", "1. 简洁明了：文案用词简练，表达清晰，让读者一目了然。\n", "2. 活泼生动：通过形象生动的词语和描述，让读者感受到户外徒步的乐趣和新奇。\n", "\n", "词汇选择分析：\n", "1. 专业性词汇：如“Advanced Chassis底盘科技”、“GORE-TEX技术”等词汇突出了产品的专业性和科技含量。\n", "2. 形象词汇：如“洒脱”、“美学”、“蜿蜒山路”等词汇增加了文案的形象感和吸引力。\n", "\n", "综合来看，该文案结构清晰，修辞手法丰富，语言风格活泼生动，词汇选择恰当。在仿写时可以借鉴其简洁明了的风格，同时注意运用比喻、排比等修辞手法，选择形象生动的词汇，以吸引读者的注意力。"]}], "source": ["query_rewrite = PromptTemplate(f\"\"\"你需要从文案结构、修辞手法、语言风格、词汇选择四个角度分析以下文案，为后续文案仿写提供指导：<文案>{product1}</文案>\"\"\")\n", "stream = await llm.astream(query_rewrite)\n", "refine_product=\"\"\n", "async for token in stream:\n", "    refine_product+=token\n", "    print(token,end=\"\")"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 15, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["在巴黎的长期深入合作，Salomon与青年文化社群Jah Jah携手推出ACS PRO FOR JAH JAH联名款。这次合作灵感源自西非神话故事Kankourang，展现了巴黎青年的自由乐观生活态度，引领年轻人和少数族裔探索新的户外运动实践。\n", "\n", "Salomon X Jah Jah的合作不仅探索西非神话与传统，更重温了西非文化故事Kankourang，讲述了一个孩子踏上内心旅程的故事。这款合作款展现了JAH JAH宇宙的价值观，将艺术创作与功能性完美结合，为日常生活注入了色彩振动。\n", "\n", "采用坚固的Kurim结构，ACS PRO FOR JAH JAH提供稳定、坚固的包覆效果，同时内外侧开口设计提供流畅的透气性。搭配Agile Chassis System缓震系统，这款鞋子以经典轮廓为基础，展现了年轻活力和冒险精神。Salomon Acs Pro For Jah Jah合作款即将于10月14日正式发售，让您感受西非文化遗产在当今艺术和社区中的引人入胜力量。"]}], "source": ["prompt2 = PromptTemplate(\"\"\"\n", "现在你是一名文案撰写专家，你需要根据给定的文案要求和文案信息撰写专业、优美的文案。\n", "<文案要求>\n", "{request}\n", "</文案要求>\n", "<文案信息>\n", "{input_data}\n", "</文案信息>\"\"\")\n", "stream = await llm.astream(prompt2,input_data=input_data,request=stream)\n", "async for token in stream:\n", "    print(token,end=\"\")"], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}