History of Antiword by (C) <PERSON><PERSON>
------------------------------------


The Name
--------
The name comes from: "The antidote against people who send Microsoft(R) Word
files to everybody, because they believe that everybody runs Windows(R) and
therefore runs Word".


Version 0.37 (21 Oct 2005)
--------------------------
Beta release, for evaluation by the public.


Known Limitations
-----------------

1) The layout of Word documents is kept secret by Microsoft(R). Therefore
   Antiword is based on information gathered from the Internet and on
   guesswork.
2) Antiword doesn't show all the images included in a Word document.
3) Antiword doesn't do any hyphenation, because hyphenation is language
   dependent.
4) Antiword places footnotes at the end of the text.
5) Antiword places box text after normal text and not in a box.
6) Antiword doesn't try to emulate any of Word's DTP abilities.
7) PostScript ouput will not work in combination with UTF-8. It only works in
   combination with character sets ISO-8859-1, ISO-8859-2 and ISO-8859-5.
8) Antiword's error messages are not very helpful.


Known Bugs
----------

1) Antiword cannot handle encrypted documents.
2) Antiword assumes default tab stops.
3) Antiword doesn't handle frames.
4) Antiword ignores page headers and footers.
5) Antiword only handles lists in some of the styles.
6) Antiword cannot handle some types of multilevel lists.
7) Antiword assumes that all Word documents made on a Macintosh with Word
   version 6 or older use the MacRoman character set.
